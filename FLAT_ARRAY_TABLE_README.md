# 一维数组多维表格

## 功能概述

这是一个基于 Vue 2 的一维数组多维表格组件，通过一维数组的数据结构来实现分组和数据行混合的多维表格展示。

## 数据结构

### 一维数组格式
```json
{
    "tableData": [
        {
            "type": "group",
            "groupName": "group1",
            "title": "江浙小吃",
            "address": "上海市普陀区真北路"
        },
        {
            "type": "item",
            "id": "12987122",
            "name": "好滋好味鸡蛋仔",
            "category": "江浙小吃",
            "desc": "荷兰优质淡奶，奶香浓而不腻",
            "address": "上海市普陀区真北路",
            "shop": "王小虎夫妻店",
            "shopId": "10333",
            "price": 15.8
        },
        {
            "type": "group",
            "groupName": "group2",
            "title": "川湘菜",
            "address": "成都市锦江区春熙路"
        },
        {
            "type": "item",
            "id": "12987123",
            "name": "麻辣小龙虾",
            "category": "川湘菜",
            "desc": "新鲜小龙虾，麻辣鲜香",
            "address": "上海市黄浦区南京路",
            "shop": "李记海鲜",
            "shopId": "10334",
            "price": 68.0
        }
    ]
}
```

### 数据类型说明

#### 分组行 (type: "group")
- `type`: 固定为 "group"
- `groupName`: 分组的唯一标识符
- `title`: 分组显示标题
- `address`: 分组地址信息

#### 数据行 (type: "item")
- `type`: 固定为 "item"
- `id`: 数据项的唯一标识符
- `name`: 商品名称
- `category`: 商品分类
- `desc`: 商品描述
- `address`: 商品地址
- `shop`: 店铺名称
- `shopId`: 店铺ID
- `price`: 商品价格

## 主要功能

### 🔄 双模式显示
1. **一维数组模式**: 直接展示原始的一维数组数据
2. **分组展示模式**: 将一维数组按分组结构重新组织展示

### 📊 数据管理
- **添加分组行**: 动态添加新的分组
- **添加数据行**: 动态添加新的数据项
- **编辑功能**: 支持编辑分组和数据项
- **删除功能**: 支持删除分组和数据项
- **排序功能**: 支持上移/下移调整数据顺序

### 💾 数据操作
- **导出功能**: 将当前数据导出为 JSON 文件
- **实时预览**: 在两种模式间切换查看效果

### 🎨 界面特性
- **类型标识**: 通过颜色标识区分分组行和数据行
- **响应式设计**: 支持移动端和桌面端
- **交互友好**: 悬停效果和操作按钮
- **编辑对话框**: 模态对话框进行数据编辑

## 技术实现

### 核心算法

#### 分组数据转换
```javascript
computed: {
    groupedDisplay() {
        const groups = []
        let currentGroup = null
        
        this.tableData.forEach((item, index) => {
            if (item.type === 'group') {
                if (currentGroup) {
                    groups.push(currentGroup)
                }
                currentGroup = {
                    ...item,
                    items: [],
                    collapsed: this.groupCollapsed[index] || false,
                    summary: null
                }
            } else if (item.type === 'item' && currentGroup) {
                currentGroup.items.push(item)
            }
        })
        
        if (currentGroup) {
            groups.push(currentGroup)
        }
        
        return groups
    }
}
```

#### 统计信息计算
```javascript
// 计算统计信息
groups.forEach(group => {
    if (group.items.length > 0) {
        const totalPrice = group.items.reduce((sum, item) => sum + (item.price || 0), 0)
        group.summary = {
            avgPrice: (totalPrice / group.items.length).toFixed(2),
            totalPrice: totalPrice.toFixed(2),
            count: group.items.length
        }
    }
})
```

### 数据操作方法

#### 添加数据
```javascript
// 添加分组行
addGroupRow() {
    const newGroup = {
        type: 'group',
        groupName: `group${Date.now()}`,
        title: '新分组',
        address: '新地址'
    }
    this.tableData.push(newGroup)
},

// 添加数据行
addDataRow() {
    const newItem = {
        type: 'item',
        id: `${Date.now()}`,
        name: '新商品',
        category: '未分类',
        desc: '新商品描述',
        address: '新地址',
        shop: '新店铺',
        shopId: `${Date.now()}`,
        price: 0
    }
    this.tableData.push(newItem)
}
```

#### 数据排序
```javascript
// 上移
moveUp(index) {
    if (index > 0) {
        const item = this.tableData.splice(index, 1)[0]
        this.tableData.splice(index - 1, 0, item)
    }
},

// 下移
moveDown(index) {
    if (index < this.tableData.length - 1) {
        const item = this.tableData.splice(index, 1)[0]
        this.tableData.splice(index + 1, 0, item)
    }
}
```

## 使用场景

### 适用场景
1. **商品分类管理**: 按类别组织商品数据
2. **订单分组展示**: 按状态或时间分组显示订单
3. **员工部门管理**: 按部门组织员工信息
4. **项目任务管理**: 按项目分组显示任务
5. **财务报表**: 按科目分组显示财务数据

### 优势特点
1. **数据结构简单**: 使用一维数组，易于存储和传输
2. **灵活性强**: 可以动态调整分组和数据的顺序
3. **扩展性好**: 容易添加新的字段和功能
4. **性能优秀**: 避免了复杂的嵌套数据结构
5. **易于理解**: 数据结构直观，便于开发和维护

## 项目运行

### 启动开发服务器
```bash
npm run serve
```

### 访问地址
```
http://localhost:8083/
```

### 功能操作
1. 切换显示模式查看不同的展示效果
2. 点击"添加分组行"创建新分组
3. 点击"添加数据行"创建新数据项
4. 使用编辑按钮修改数据
5. 使用上移/下移按钮调整顺序
6. 点击"导出数据"下载 JSON 文件

## 扩展建议

### 功能增强
1. **批量操作**: 支持批量删除、移动
2. **拖拽排序**: 支持鼠标拖拽调整顺序
3. **数据验证**: 添加表单验证功能
4. **搜索过滤**: 支持按关键词搜索
5. **导入功能**: 支持从文件导入数据

### 性能优化
1. **虚拟滚动**: 处理大量数据时的性能优化
2. **懒加载**: 分组内容的懒加载
3. **缓存机制**: 计算结果的缓存
4. **防抖处理**: 频繁操作的防抖处理

这个一维数组多维表格组件提供了一种简单而强大的方式来处理分组数据，特别适合需要灵活调整数据结构和展示方式的场景。
