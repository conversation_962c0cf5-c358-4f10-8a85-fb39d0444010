<template>
    <div class="grouped-table-demo">
        <div class="demo-header">
            <h1>Vue 2 分组表格演示</h1>
            <p>这是一个功能完整的分组表格组件，支持多种分组方式和统计功能</p>
        </div>
        
        <!-- 分组控制器 -->
        <div class="group-controls">
            <div class="control-item">
                <label>分组字段：</label>
                <select v-model="groupBy" @change="handleGroupChange" class="group-select">
                    <option value="">不分组</option>
                    <option value="category">按分类分组</option>
                    <option value="shop">按店铺分组</option>
                    <option value="address">按地址分组</option>
                </select>
            </div>
            
            <div class="control-item" v-if="groupBy">
                <label>
                    <input type="checkbox" v-model="showGroupSummary" @change="handleSummaryChange">
                    显示分组统计
                </label>
            </div>
            
            <div class="control-item" v-if="groupBy">
                <button @click="expandAllGroups" class="btn btn-small">展开全部</button>
                <button @click="collapseAllGroups" class="btn btn-small">收起全部</button>
            </div>
        </div>

        <!-- 分组表格 -->
        <div v-if="groupBy" class="grouped-table">
            <div v-for="(group, groupKey) in groupedData" :key="groupKey" class="group-section">
                <!-- 分组标题 -->
                <div class="group-header" @click="toggleGroup(groupKey)">
                    <span :class="['arrow', group.collapsed ? 'arrow-right' : 'arrow-down']">▶</span>
                    <span class="group-title">{{ groupKey }}</span>
                    <span class="group-count">({{ group.items.length }}项)</span>
                    <div class="group-statistics" v-if="showGroupSummary">
                        <span>总计: {{ group.items.length }} 条记录</span>
                        <span v-if="group.summary" class="summary-info">
                            | 平均价格: ¥{{ group.summary.avgPrice }}
                        </span>
                    </div>
                </div>
                
                <!-- 分组内容 -->
                <div v-show="!group.collapsed" class="group-content">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th width="120">商品 ID</th>
                                <th width="150">商品名称</th>
                                <th width="120">分类</th>
                                <th width="120">店铺</th>
                                <th width="100">价格</th>
                                <th>描述</th>
                                <th width="100">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="item in group.items" :key="item.id" class="data-row">
                                <td>{{ item.id }}</td>
                                <td>{{ item.name }}</td>
                                <td>{{ item.category }}</td>
                                <td>{{ item.shop }}</td>
                                <td class="price">¥{{ item.price }}</td>
                                <td class="desc" :title="item.desc">{{ item.desc }}</td>
                                <td>
                                    <button @click="editItem(item)" class="btn btn-small btn-primary">编辑</button>
                                    <button @click="deleteItem(item)" class="btn btn-small btn-danger">删除</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 普通表格（无分组） -->
        <div v-else class="normal-table">
            <table class="data-table">
                <thead>
                    <tr>
                        <th width="120">商品 ID</th>
                        <th width="150">商品名称</th>
                        <th width="120">分类</th>
                        <th width="120">店铺</th>
                        <th width="100">价格</th>
                        <th width="150">地址</th>
                        <th>描述</th>
                        <th width="100">操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="item in tableData" :key="item.id" class="data-row">
                        <td>{{ item.id }}</td>
                        <td>{{ item.name }}</td>
                        <td>{{ item.category }}</td>
                        <td>{{ item.shop }}</td>
                        <td class="price">¥{{ item.price }}</td>
                        <td>{{ item.address }}</td>
                        <td class="desc" :title="item.desc">{{ item.desc }}</td>
                        <td>
                            <button @click="editItem(item)" class="btn btn-small btn-primary">编辑</button>
                            <button @click="deleteItem(item)" class="btn btn-small btn-danger">删除</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</template>

<script>
export default {
    name: 'GroupedTableDemo',
    data() {
        return {
            groupBy: '', // 分组字段
            groupCollapsed: {}, // 记录各分组的折叠状态
            showGroupSummary: true, // 是否显示分组统计
            tableData: [
                {
                    id: '12987122',
                    name: '好滋好味鸡蛋仔',
                    category: '江浙小吃',
                    desc: '荷兰优质淡奶，奶香浓而不腻',
                    address: '上海市普陀区真北路',
                    shop: '王小虎夫妻店',
                    shopId: '10333',
                    price: 15.8
                },
                {
                    id: '12987123',
                    name: '麻辣小龙虾',
                    category: '川湘菜',
                    desc: '新鲜小龙虾，麻辣鲜香',
                    address: '上海市黄浦区南京路',
                    shop: '李记海鲜',
                    shopId: '10334',
                    price: 68.0
                },
                {
                    id: '12987124',
                    name: '北京烤鸭',
                    category: '京菜',
                    desc: '正宗北京烤鸭，皮脆肉嫩',
                    address: '北京市朝阳区建国路',
                    shop: '全聚德',
                    shopId: '10335',
                    price: 128.0
                },
                {
                    id: '12987125',
                    name: '蒸蛋羹',
                    category: '江浙小吃',
                    desc: '嫩滑蒸蛋，营养丰富',
                    address: '上海市普陀区真北路',
                    shop: '王小虎夫妻店',
                    shopId: '10333',
                    price: 12.0
                },
                {
                    id: '12987126',
                    name: '麻婆豆腐',
                    category: '川湘菜',
                    desc: '经典川菜，麻辣鲜香',
                    address: '成都市锦江区春熙路',
                    shop: '川味轩',
                    shopId: '10336',
                    price: 28.0
                },
                {
                    id: '12987127',
                    name: '宫保鸡丁',
                    category: '川湘菜',
                    desc: '传统川菜，酸甜可口',
                    address: '上海市黄浦区南京路',
                    shop: '李记海鲜',
                    shopId: '10334',
                    price: 32.0
                },
                {
                    id: '12987128',
                    name: '小笼包',
                    category: '江浙小吃',
                    desc: '皮薄馅大，汤汁鲜美',
                    address: '上海市黄浦区南京路',
                    shop: '南翔小笼',
                    shopId: '10337',
                    price: 25.0
                },
                {
                    id: '12987129',
                    name: '炸酱面',
                    category: '京菜',
                    desc: '老北京传统面食',
                    address: '北京市东城区王府井',
                    shop: '老北京面馆',
                    shopId: '10338',
                    price: 18.0
                }
            ]
        }
    },
    computed: {
        // 分组后的数据
        groupedData() {
            if (!this.groupBy) return {}

            const groups = {}
            this.tableData.forEach(item => {
                const groupKey = item[this.groupBy] || '未分类'
                if (!groups[groupKey]) {
                    groups[groupKey] = {
                        items: [],
                        collapsed: this.groupCollapsed[groupKey] || false,
                        summary: null
                    }
                }
                groups[groupKey].items.push(item)
            })

            // 计算分组统计信息
            Object.keys(groups).forEach(groupKey => {
                const items = groups[groupKey].items
                const totalPrice = items.reduce((sum, item) => sum + item.price, 0)
                groups[groupKey].summary = {
                    avgPrice: (totalPrice / items.length).toFixed(2),
                    totalPrice: totalPrice.toFixed(2),
                    count: items.length
                }
            })

            return groups
        }
    },
    methods: {
        // 处理分组变化
        handleGroupChange(value) {
            this.groupBy = value
            // 重置折叠状态
            this.groupCollapsed = {}
        },

        // 处理统计显示变化
        handleSummaryChange() {
            // 可以在这里添加额外的逻辑
        },

        // 切换分组折叠状态
        toggleGroup(groupKey) {
            this.$set(this.groupCollapsed, groupKey, !this.groupCollapsed[groupKey])
        },

        // 展开所有分组
        expandAllGroups() {
            Object.keys(this.groupedData).forEach(groupKey => {
                this.$set(this.groupCollapsed, groupKey, false)
            })
        },

        // 收起所有分组
        collapseAllGroups() {
            Object.keys(this.groupedData).forEach(groupKey => {
                this.$set(this.groupCollapsed, groupKey, true)
            })
        },

        // 编辑项目
        editItem(item) {
            alert(`编辑商品: ${item.name}`)
            // 这里可以打开编辑对话框或跳转到编辑页面
        },

        // 删除项目
        deleteItem(item) {
            if (confirm(`确定要删除商品 "${item.name}" 吗？`)) {
                const index = this.tableData.findIndex(data => data.id === item.id)
                if (index > -1) {
                    this.tableData.splice(index, 1)
                }
            }
        }
    }
}
</script>

<style scoped>
.grouped-table-demo {
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.demo-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px;
}

.demo-header h1 {
    margin: 0 0 10px 0;
    font-size: 28px;
    font-weight: 600;
}

.demo-header p {
    margin: 0;
    font-size: 16px;
    opacity: 0.9;
}

.group-controls {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f5f7fa;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
}

.control-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.control-item label {
    font-weight: 500;
    color: #606266;
    font-size: 14px;
}

.group-select {
    padding: 8px 12px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    background-color: #fff;
    font-size: 14px;
    min-width: 150px;
}

.group-select:focus {
    outline: none;
    border-color: #409eff;
}

.btn {
    padding: 6px 12px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    background-color: #fff;
    color: #606266;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s;
}

.btn:hover {
    background-color: #f5f7fa;
}

.btn-small {
    padding: 4px 8px;
    font-size: 12px;
}

.btn-primary {
    background-color: #409eff;
    border-color: #409eff;
    color: #fff;
}

.btn-primary:hover {
    background-color: #66b1ff;
}

.btn-danger {
    background-color: #f56c6c;
    border-color: #f56c6c;
    color: #fff;
}

.btn-danger:hover {
    background-color: #f78989;
}

.grouped-table {
    border: 1px solid #ebeef5;
    border-radius: 8px;
    overflow: hidden;
}

.group-section {
    border-bottom: 1px solid #ebeef5;
}

.group-section:last-child {
    border-bottom: none;
}

.group-header {
    padding: 12px 16px;
    background-color: #fafafa;
    border-bottom: 1px solid #ebeef5;
    cursor: pointer;
    display: flex;
    align-items: center;
    transition: background-color 0.3s;
    user-select: none;
}

.group-header:hover {
    background-color: #f0f2f5;
}

.arrow {
    font-size: 12px;
    color: #909399;
    transition: transform 0.3s;
    margin-right: 8px;
}

.arrow-right {
    transform: rotate(0deg);
}

.arrow-down {
    transform: rotate(90deg);
}

.group-title {
    font-weight: 600;
    color: #303133;
    font-size: 14px;
}

.group-count {
    margin-left: 8px;
    color: #909399;
    font-size: 12px;
}

.group-statistics {
    margin-left: auto;
    color: #606266;
    font-size: 12px;
}

.summary-info {
    margin-left: 10px;
    color: #409eff;
}

.group-content {
    background-color: #fff;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.data-table th {
    background-color: #fafafa;
    color: #909399;
    font-weight: 500;
    padding: 12px 8px;
    text-align: left;
    border-bottom: 1px solid #ebeef5;
}

.data-table td {
    padding: 12px 8px;
    border-bottom: 1px solid #f0f0f0;
    color: #606266;
}

.data-row:hover {
    background-color: #f5f7fa;
}

.price {
    font-weight: 600;
    color: #e6a23c;
}

.desc {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.normal-table {
    margin-top: 20px;
    border: 1px solid #ebeef5;
    border-radius: 8px;
    overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .group-controls {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .control-item {
        width: 100%;
        justify-content: space-between;
    }

    .group-select {
        min-width: 120px;
    }

    .data-table {
        font-size: 12px;
    }

    .data-table th,
    .data-table td {
        padding: 8px 4px;
    }

    .desc {
        max-width: 100px;
    }
}

@media (max-width: 480px) {
    .grouped-table-demo {
        padding: 10px;
    }

    .group-controls {
        padding: 10px;
    }

    .data-table th,
    .data-table td {
        padding: 6px 2px;
        font-size: 11px;
    }

    .btn-small {
        padding: 2px 4px;
        font-size: 10px;
    }
}
</style>
