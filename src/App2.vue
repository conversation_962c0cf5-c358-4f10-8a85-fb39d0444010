<template>
    <div class="grouped-table-container">
        <!-- 分组控制器 -->
        <div class="group-controls">
            <el-select v-model="groupBy" placeholder="选择分组字段" @change="handleGroupChange" clearable>
                <el-option label="不分组" value=""></el-option>
                <el-option label="按分类分组" value="category"></el-option>
                <el-option label="按店铺分组" value="shop"></el-option>
                <el-option label="按地址分组" value="address"></el-option>
            </el-select>
        </div>

        <!-- 分组表格 -->
        <div v-if="groupBy" class="grouped-table">
            <div v-for="(group, groupKey) in groupedData" :key="groupKey" class="group-section">
                <!-- 分组标题 -->
                <div class="group-header" @click="toggleGroup(groupKey)">
                    <i :class="group.collapsed ? 'el-icon-arrow-right' : 'el-icon-arrow-down'"></i>
                    <span class="group-title">{{ groupKey }}</span>
                    <span class="group-count">({{ group.items.length }}项)</span>
                    <div class="group-statistics">
                        <span>总计: {{ group.items.length }} 条记录</span>
                    </div>
                </div>
                <!-- 分组内容 -->
                <div v-show="!group.collapsed" class="group-content">
                    <el-table :data="group.items" style="width: 100%" size="small">
                        <el-table-column label="商品 ID" prop="id" width="120"></el-table-column>
                        <el-table-column label="商品名称" prop="name" width="150"></el-table-column>
                        <el-table-column label="描述" prop="desc" show-overflow-tooltip></el-table-column>
                    </el-table>
                </div>
            </div>
        </div>

        <!-- 普通表格（无分组） -->
        <div v-else class="normal-table">
            <el-table :data="tableData" style="width: 100%">
                <el-table-column type="expand">
                    <template slot-scope="props">
                        呵呵呵
                    </template>
                </el-table-column>
                <el-table-column label="商品 ID" prop="id" width="120"></el-table-column>
                <el-table-column label="商品名称" prop="name" width="150"></el-table-column>
                <el-table-column label="分类" prop="category" width="180"></el-table-column>
                <el-table-column label="店铺" prop="shop" width="120"></el-table-column>
                <el-table-column label="描述" prop="desc" show-overflow-tooltip></el-table-column>
            </el-table>
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            groupBy: '', // 分组字段
            groupCollapsed: {}, // 记录各分组的折叠状态
            tableData: [
                {
                    id: '12987122',
                    name: '好滋好味鸡蛋仔',
                    category: '江浙小吃',
                    desc: '荷兰优质淡奶，奶香浓而不腻',
                    address: '上海市普陀区真北路',
                    shop: '王小虎夫妻店',
                    shopId: '10333'
                },
                {
                    id: '12987123',
                    name: '麻辣小龙虾',
                    category: '川湘菜',
                    desc: '新鲜小龙虾，麻辣鲜香',
                    address: '上海市黄浦区南京路',
                    shop: '李记海鲜',
                    shopId: '10334'
                },
                {
                    id: '12987124',
                    name: '北京烤鸭',
                    category: '京菜',
                    desc: '正宗北京烤鸭，皮脆肉嫩',
                    address: '北京市朝阳区建国路',
                    shop: '全聚德',
                    shopId: '10335'
                },
                {
                    id: '12987125',
                    name: '蒸蛋羹',
                    category: '江浙小吃',
                    desc: '嫩滑蒸蛋，营养丰富',
                    address: '上海市普陀区真北路',
                    shop: '王小虎夫妻店',
                    shopId: '10333'
                },
                {
                    id: '12987126',
                    name: '麻婆豆腐',
                    category: '川湘菜',
                    desc: '经典川菜，麻辣鲜香',
                    address: '成都市锦江区春熙路',
                    shop: '川味轩',
                    shopId: '10336'
                },
                {
                    id: '12987127',
                    name: '宫保鸡丁',
                    category: '川湘菜',
                    desc: '传统川菜，酸甜可口',
                    address: '上海市黄浦区南京路',
                    shop: '李记海鲜',
                    shopId: '10334'
                }
            ]
        }
    },
    computed: {
        // 分组后的数据
        groupedData() {
            if (!this.groupBy) return {}

            const groups = {}
            this.tableData.forEach(item => {
                const groupKey = item[this.groupBy] || '未分类'
                if (!groups[groupKey]) {
                    groups[groupKey] = {
                        items: [],
                        collapsed: this.groupCollapsed[groupKey] || false
                    }
                }
                groups[groupKey].items.push(item)
            })

            return groups
        }
    },
    methods: {
        // 处理分组变化
        handleGroupChange(value) {
            this.groupBy = value
            // 重置折叠状态
            this.groupCollapsed = {}
        },

        // 切换分组折叠状态
        toggleGroup(groupKey) {
            this.$set(this.groupCollapsed, groupKey, !this.groupCollapsed[groupKey])
        },
    }
}
</script>
<style>
.grouped-table-container {
    padding: 20px;
}

.group-controls {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f5f7fa;
    border-radius: 4px;
    display: flex;
    align-items: center;
}

.grouped-table {
    border: 1px solid #ebeef5;
    border-radius: 4px;
}

.group-section {
    border-bottom: 1px solid #ebeef5;
}

.group-section:last-child {
    border-bottom: none;
}

.group-header {
    padding: 12px 16px;
    background-color: #fafafa;
    border-bottom: 1px solid #ebeef5;
    cursor: pointer;
    display: flex;
    align-items: center;
    transition: background-color 0.3s;
}

.group-header:hover {
    background-color: #f0f2f5;
}

.group-title {
    font-weight: 600;
    color: #303133;
    margin-left: 8px;
    font-size: 14px;
}

.group-count {
    margin-left: 8px;
    color: #909399;
    font-size: 12px;
}

.group-statistics {
    margin-left: auto;
    color: #606266;
    font-size: 12px;
}

.group-content {
    background-color: #fff;
}

.group-content .el-table {
    border: none;
}

.group-content .el-table::before {
    display: none;
}

.demo-table-expand {
    font-size: 0;
}

.demo-table-expand label {
    width: 90px;
    color: #99a9bf;
}

.demo-table-expand .el-form-item {
    margin-right: 0;
    margin-bottom: 0;
    width: 50%;
}

.normal-table {
    margin-top: 20px;
}

/* 分组表格内的表格样式调整 */
.group-content .el-table th {
    background-color: #fafafa;
}

.group-content .el-table td,
.group-content .el-table th {
    border-bottom: 1px solid #f0f0f0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .group-controls {
        flex-direction: column;
        align-items: flex-start;
    }

    .group-controls .el-checkbox {
        margin-left: 0 !important;
        margin-top: 10px;
    }
}
</style>