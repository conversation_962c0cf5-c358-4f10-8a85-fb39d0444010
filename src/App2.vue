<template>
    <div class="flat-array-table">
        <div class="demo-header">
            <h1>一维数组多维表格</h1>
            <p>通过一维数组实现分组和数据行混合的多维表格展示</p>
        </div>
        
        <!-- 控制面板 -->
        <el-card class="controls" shadow="never">
            <div class="control-item">
                <span class="control-label">显示模式：</span>
                <el-select v-model="displayMode" @change="handleModeChange" placeholder="请选择显示模式" style="width: 150px;">
                    <el-option label="一维数组模式" value="flat"></el-option>
                    <el-option label="分组展示模式" value="grouped"></el-option>
                </el-select>
            </div>

            <div class="control-item">
                <el-button type="primary" icon="el-icon-plus" @click="addGroupRow">添加分组行</el-button>
                <el-button type="success" icon="el-icon-document-add" @click="addDataRow">添加数据行</el-button>
                <el-button type="info" icon="el-icon-download" @click="exportData">导出数据</el-button>
            </div>
        </el-card>

        <!-- 一维数组表格 -->
        <div v-if="displayMode === 'flat'" class="flat-table">
            <el-table :data="tableData" border stripe style="width: 100%"
                      :row-class-name="getRowClassName">
                <el-table-column label="类型" width="80" align="center">
                    <template slot-scope="scope">
                        <el-tag :type="scope.row.type === 'group' ? 'primary' : 'success'" size="mini">
                            {{ scope.row.type === 'group' ? '分组' : '数据' }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="ID/分组名" width="120" prop="id">
                    <template slot-scope="scope">
                        {{ scope.row.type === 'group' ? scope.row.groupName : scope.row.id }}
                    </template>
                </el-table-column>
                <el-table-column label="名称/标题" width="150">
                    <template slot-scope="scope">
                        {{ scope.row.type === 'group' ? scope.row.title : scope.row.name }}
                    </template>
                </el-table-column>
                <el-table-column label="分类" width="120" prop="category">
                    <template slot-scope="scope">
                        {{ scope.row.category || '-' }}
                    </template>
                </el-table-column>
                <el-table-column label="店铺" width="120" prop="shop">
                    <template slot-scope="scope">
                        {{ scope.row.shop || '-' }}
                    </template>
                </el-table-column>
                <el-table-column label="价格" width="100" align="right">
                    <template slot-scope="scope">
                        <span v-if="scope.row.price" class="price">¥{{ scope.row.price }}</span>
                        <span v-else>-</span>
                    </template>
                </el-table-column>
                <el-table-column label="描述/地址" show-overflow-tooltip>
                    <template slot-scope="scope">
                        {{ scope.row.desc || scope.row.address || '-' }}
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="200" align="center">
                    <template slot-scope="scope">
                        <el-button size="mini" type="primary" icon="el-icon-edit" @click="editItem(scope.row, scope.$index)">编辑</el-button>
                        <el-button size="mini" type="danger" icon="el-icon-delete" @click="deleteItem(scope.$index)">删除</el-button>
                        <el-button size="mini" icon="el-icon-top" @click="moveUp(scope.$index)" :disabled="scope.$index === 0">上移</el-button>
                        <el-button size="mini" icon="el-icon-bottom" @click="moveDown(scope.$index)" :disabled="scope.$index === tableData.length - 1">下移</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <!-- 分组展示模式 -->
        <div v-else class="grouped-display">
            <el-collapse v-model="activeGroups" accordion>
                <el-collapse-item v-for="(group, groupIndex) in groupedDisplay" :key="groupIndex" :name="groupIndex">
                    <template slot="title">
                        <div class="group-header-content">
                            <el-tag type="primary" size="medium">{{ group.title }}</el-tag>
                            <el-badge :value="group.items.length" class="group-badge">
                                <span class="group-count-text">条记录</span>
                            </el-badge>
                            <div class="group-statistics" v-if="group.summary">
                                <span class="summary-text">平均价格: </span>
                                <el-tag type="warning" size="mini">¥{{ group.summary.avgPrice }}</el-tag>
                            </div>
                        </div>
                    </template>

                    <!-- 分组内容 -->
                    <el-table :data="group.items" border stripe style="width: 100%">
                        <el-table-column label="商品 ID" width="120" prop="id"></el-table-column>
                        <el-table-column label="商品名称" width="150" prop="name"></el-table-column>
                        <el-table-column label="分类" width="120" prop="category"></el-table-column>
                        <el-table-column label="店铺" width="120" prop="shop"></el-table-column>
                        <el-table-column label="价格" width="100" align="right">
                            <template slot-scope="scope">
                                <span class="price">¥{{ scope.row.price }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="描述" prop="desc" show-overflow-tooltip></el-table-column>
                        <el-table-column label="操作" width="150" align="center">
                            <template slot-scope="scope">
                                <el-button size="mini" type="primary" icon="el-icon-edit" @click="editDataItem(scope.row)">编辑</el-button>
                                <el-button size="mini" type="danger" icon="el-icon-delete" @click="deleteDataItem(scope.row)">删除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-collapse-item>
            </el-collapse>
        </div>

        <!-- 编辑对话框 -->
        <el-dialog
            :title="editingItem.type === 'group' ? '编辑分组' : '编辑数据'"
            :visible.sync="showEditDialog"
            width="500px"
            :before-close="closeEditDialog">
            <el-form :model="editingItem" label-width="100px" label-position="left">
                <el-form-item label="分组名称:" v-if="editingItem.type === 'group'">
                    <el-input v-model="editingItem.groupName" placeholder="请输入分组名称"></el-input>
                </el-form-item>
                <el-form-item label="分组标题:" v-if="editingItem.type === 'group'">
                    <el-input v-model="editingItem.title" placeholder="请输入分组标题"></el-input>
                </el-form-item>
                <el-form-item label="商品ID:" v-if="editingItem.type === 'item'">
                    <el-input v-model="editingItem.id" placeholder="请输入商品ID"></el-input>
                </el-form-item>
                <el-form-item label="商品名称:" v-if="editingItem.type === 'item'">
                    <el-input v-model="editingItem.name" placeholder="请输入商品名称"></el-input>
                </el-form-item>
                <el-form-item label="分类:" v-if="editingItem.type === 'item'">
                    <el-input v-model="editingItem.category" placeholder="请输入分类"></el-input>
                </el-form-item>
                <el-form-item label="店铺:" v-if="editingItem.type === 'item'">
                    <el-input v-model="editingItem.shop" placeholder="请输入店铺名称"></el-input>
                </el-form-item>
                <el-form-item label="价格:" v-if="editingItem.type === 'item'">
                    <el-input-number v-model="editingItem.price" :precision="2" :step="0.01" :min="0" placeholder="请输入价格"></el-input-number>
                </el-form-item>
                <el-form-item :label="editingItem.type === 'group' ? '地址:' : '描述:'">
                    <el-input
                        type="textarea"
                        :rows="3"
                        v-model="editingDescOrAddress"
                        :placeholder="editingItem.type === 'group' ? '请输入地址' : '请输入描述'">
                    </el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="closeEditDialog">取 消</el-button>
                <el-button type="primary" @click="saveEdit">确 定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: 'FlatArrayTable',
    data() {
        return {
            displayMode: 'flat', // 'flat' 或 'grouped'
            groupCollapsed: {}, // 分组折叠状态
            activeGroups: [], // 激活的分组（用于 el-collapse）
            showEditDialog: false,
            editingItem: {},
            editingIndex: -1,
            tableData: [
                // 分组1
                {
                    type: 'group',
                    groupName: 'group1',
                    title: '江浙小吃',
                    address: '上海市普陀区真北路'
                },
                // 分组1的数据
                {
                    type: 'item',
                    id: '12987122',
                    name: '好滋好味鸡蛋仔',
                    category: '江浙小吃',
                    desc: '荷兰优质淡奶，奶香浓而不腻',
                    address: '上海市普陀区真北路',
                    shop: '王小虎夫妻店',
                    shopId: '10333',
                    price: 15.8
                },
                {
                    type: 'item',
                    id: '12987125',
                    name: '蒸蛋羹',
                    category: '江浙小吃',
                    desc: '嫩滑蒸蛋，营养丰富',
                    address: '上海市普陀区真北路',
                    shop: '王小虎夫妻店',
                    shopId: '10333',
                    price: 12.0
                },
                // 分组2
                {
                    type: 'group',
                    groupName: 'group2',
                    title: '川湘菜',
                    address: '成都市锦江区春熙路'
                },
                // 分组2的数据
                {
                    type: 'item',
                    id: '12987123',
                    name: '麻辣小龙虾',
                    category: '川湘菜',
                    desc: '新鲜小龙虾，麻辣鲜香',
                    address: '上海市黄浦区南京路',
                    shop: '李记海鲜',
                    shopId: '10334',
                    price: 68.0
                },
                {
                    type: 'item',
                    id: '12987126',
                    name: '麻婆豆腐',
                    category: '川湘菜',
                    desc: '经典川菜，麻辣鲜香',
                    address: '成都市锦江区春熙路',
                    shop: '川味轩',
                    shopId: '10336',
                    price: 28.0
                },
                // 分组3
                {
                    type: 'group',
                    groupName: 'group3',
                    title: '京菜',
                    address: '北京市朝阳区建国路'
                },
                // 分组3的数据
                {
                    type: 'item',
                    id: '12987124',
                    name: '北京烤鸭',
                    category: '京菜',
                    desc: '正宗北京烤鸭，皮脆肉嫩',
                    address: '北京市朝阳区建国路',
                    shop: '全聚德',
                    shopId: '10335',
                    price: 128.0
                }
            ]
        }
    },
    computed: {
        // 分组展示数据
        groupedDisplay() {
            const groups = []
            let currentGroup = null
            
            this.tableData.forEach((item, index) => {
                if (item.type === 'group') {
                    if (currentGroup) {
                        groups.push(currentGroup)
                    }
                    currentGroup = {
                        ...item,
                        items: [],
                        collapsed: this.groupCollapsed[index] || false,
                        summary: null
                    }
                } else if (item.type === 'item' && currentGroup) {
                    currentGroup.items.push(item)
                }
            })
            
            if (currentGroup) {
                groups.push(currentGroup)
            }
            
            // 计算统计信息
            groups.forEach(group => {
                if (group.items.length > 0) {
                    const totalPrice = group.items.reduce((sum, item) => sum + (item.price || 0), 0)
                    group.summary = {
                        avgPrice: (totalPrice / group.items.length).toFixed(2),
                        totalPrice: totalPrice.toFixed(2),
                        count: group.items.length
                    }
                }
            })
            
            return groups
        },

        // 编辑项的描述或地址
        editingDescOrAddress: {
            get() {
                if (this.editingItem.type === 'group') {
                    return this.editingItem.address || ''
                } else {
                    return this.editingItem.desc || ''
                }
            },
            set(value) {
                if (this.editingItem.type === 'group') {
                    this.$set(this.editingItem, 'address', value)
                } else {
                    this.$set(this.editingItem, 'desc', value)
                }
            }
        }
    },
    methods: {
        // 处理显示模式变化
        handleModeChange() {
            // 可以在这里添加模式切换的逻辑
        },

        // 获取表格行的样式类名
        getRowClassName({ row, rowIndex }) {
            if (row.type === 'group') {
                return 'group-row'
            } else {
                return 'item-row'
            }
        },

        // 切换分组折叠状态
        toggleGroup(groupIndex) {
            this.$set(this.groupCollapsed, groupIndex, !this.groupCollapsed[groupIndex])
        },

        // 添加分组行
        addGroupRow() {
            const newGroup = {
                type: 'group',
                groupName: `group${Date.now()}`,
                title: '新分组',
                address: '新地址'
            }
            this.tableData.push(newGroup)
        },

        // 添加数据行
        addDataRow() {
            const newItem = {
                type: 'item',
                id: `${Date.now()}`,
                name: '新商品',
                category: '未分类',
                desc: '新商品描述',
                address: '新地址',
                shop: '新店铺',
                shopId: `${Date.now()}`,
                price: 0
            }
            this.tableData.push(newItem)
        },

        // 编辑项目
        editItem(item, index) {
            this.editingItem = { ...item }
            this.editingIndex = index
            this.showEditDialog = true
        },

        // 编辑数据项目（分组模式下）
        editDataItem(item) {
            const index = this.tableData.findIndex(data => data.id === item.id && data.type === 'item')
            if (index > -1) {
                this.editItem(item, index)
            }
        },

        // 保存编辑
        saveEdit() {
            if (this.editingIndex > -1) {
                this.$set(this.tableData, this.editingIndex, { ...this.editingItem })
            }
            this.closeEditDialog()
        },

        // 关闭编辑对话框
        closeEditDialog() {
            this.showEditDialog = false
            this.editingItem = {}
            this.editingIndex = -1
        },

        // 删除项目
        deleteItem(index) {
            const item = this.tableData[index]
            const itemType = item.type === 'group' ? '分组' : '数据项'
            const itemName = item.type === 'group' ? item.title : item.name

            if (confirm(`确定要删除${itemType} "${itemName}" 吗？`)) {
                this.tableData.splice(index, 1)
            }
        },

        // 删除数据项目（分组模式下）
        deleteDataItem(item) {
            const index = this.tableData.findIndex(data => data.id === item.id && data.type === 'item')
            if (index > -1) {
                this.deleteItem(index)
            }
        },

        // 上移
        moveUp(index) {
            if (index > 0) {
                const item = this.tableData.splice(index, 1)[0]
                this.tableData.splice(index - 1, 0, item)
            }
        },

        // 下移
        moveDown(index) {
            if (index < this.tableData.length - 1) {
                const item = this.tableData.splice(index, 1)[0]
                this.tableData.splice(index + 1, 0, item)
            }
        },

        // 导出数据
        exportData() {
            const dataStr = JSON.stringify({ tableData: this.tableData }, null, 2)
            const blob = new Blob([dataStr], { type: 'application/json' })
            const url = URL.createObjectURL(blob)
            const a = document.createElement('a')
            a.href = url
            a.download = 'flat-array-table-data.json'
            document.body.appendChild(a)
            a.click()
            document.body.removeChild(a)
            URL.revokeObjectURL(url)
        }
    }
}
</script>

<style scoped>
.flat-array-table {
    padding: 20px;
}

.demo-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px;
}

.demo-header h1 {
    margin: 0 0 10px 0;
    font-size: 28px;
    font-weight: 600;
}

.demo-header p {
    margin: 0;
    font-size: 16px;
    opacity: 0.9;
}

.controls {
    margin-bottom: 20px;
}

.controls .el-card {
    padding: 15px;
}

.controls .control-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.controls .control-item:last-child {
    margin-bottom: 0;
}

.control-label {
    font-weight: 500;
    color: #606266;
    font-size: 14px;
    min-width: 80px;
}

/* 表格行样式 */
.flat-table .el-table .group-row {
    background-color: #f0f9ff !important;
}

.flat-table .el-table .item-row {
    background-color: #fff !important;
}

/* 分组展示样式 */
.grouped-display {
    margin-top: 20px;
}

.group-header-content {
    display: flex;
    align-items: center;
    gap: 10px;
    width: 100%;
}

.group-badge {
    margin-left: auto;
}

.group-count-text {
    font-size: 12px;
    color: #909399;
}

.group-statistics {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-left: 10px;
}

.summary-text {
    font-size: 12px;
    color: #606266;
}

.price {
    font-weight: 600;
    color: #e6a23c;
}

.desc {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 分组展示样式 */
.grouped-display {
    border: 1px solid #ebeef5;
    border-radius: 8px;
    overflow: hidden;
}

.group-section {
    border-bottom: 1px solid #ebeef5;
}

.group-section:last-child {
    border-bottom: none;
}

.group-header {
    padding: 12px 16px;
    background-color: #fafafa;
    border-bottom: 1px solid #ebeef5;
    cursor: pointer;
    display: flex;
    align-items: center;
    transition: background-color 0.3s;
    user-select: none;
}

.group-header:hover {
    background-color: #f0f2f5;
}

.arrow {
    font-size: 12px;
    color: #909399;
    transition: transform 0.3s;
    margin-right: 8px;
}

.arrow-right {
    transform: rotate(0deg);
}

.arrow-down {
    transform: rotate(90deg);
}

.group-title {
    font-weight: 600;
    color: #303133;
    font-size: 14px;
}

.group-count {
    margin-left: 8px;
    color: #909399;
    font-size: 12px;
}

.group-statistics {
    margin-left: auto;
    color: #606266;
    font-size: 12px;
}

.summary-info {
    margin-left: 10px;
    color: #409eff;
}

.group-content {
    background-color: #fff;
}

/* 编辑对话框样式 */
.edit-dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.edit-dialog {
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    min-width: 400px;
    max-width: 500px;
    max-height: 80vh;
    overflow-y: auto;
}

.edit-dialog h3 {
    margin: 0 0 20px 0;
    color: #303133;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #606266;
}

.form-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    font-size: 14px;
    box-sizing: border-box;
}

.form-input:focus {
    outline: none;
    border-color: #409eff;
}

.form-textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    font-size: 14px;
    min-height: 80px;
    resize: vertical;
    box-sizing: border-box;
}

.form-textarea:focus {
    outline: none;
    border-color: #409eff;
}

.dialog-actions {
    margin-top: 20px;
    text-align: right;
}

.dialog-actions .btn {
    margin-left: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .controls {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .control-item {
        width: 100%;
        justify-content: space-between;
    }

    .data-table {
        font-size: 12px;
    }

    .data-table th,
    .data-table td {
        padding: 8px 4px;
    }

    .desc {
        max-width: 100px;
    }

    .edit-dialog {
        min-width: 300px;
        margin: 20px;
    }
}

@media (max-width: 480px) {
    .flat-array-table {
        padding: 10px;
    }

    .controls {
        padding: 10px;
    }

    .data-table th,
    .data-table td {
        padding: 6px 2px;
        font-size: 11px;
    }

    .btn-small {
        padding: 2px 4px;
        font-size: 10px;
    }
}
</style>



<!-- {
    "tableData": [
        {
            // 分组1
        },
        {
            // 分组2
        },
        {
            "id": "12987122",
            "name": "好滋好味鸡蛋仔",
            "category": "江浙小吃",
            "desc": "荷兰优质淡奶，奶香浓而不腻",
            "address": "上海市普陀区真北路",
            "shop": "王小虎夫妻店",
            "shopId": "10333",
            "price": 15.8
        },
        {
            // 分组1
        },
        {
            // 分组2
        },
        {
            "id": "12987123",
            "name": "麻辣小龙虾",
            "category": "川湘菜",
            "desc": "新鲜小龙虾，麻辣鲜香",
            "address": "上海市黄浦区南京路",
            "shop": "李记海鲜",
            "shopId": "10334",
            "price": 68.0
        }
    ]
} -->