<template>
    <div class="flat-array-table">
        <div class="demo-header">
            <h1>一维数组多维表格</h1>
            <p>通过一维数组实现分组和数据行混合的多维表格展示</p>
        </div>

        <!-- 控制面板 -->
        <el-card class="controls" shadow="never">
            <div class="control-item">
                <span class="control-label">显示模式：</span>
                <el-select v-model="displayMode" @change="handleModeChange" placeholder="请选择显示模式" style="width: 150px;">
                    <el-option label="一维数组模式" value="flat"></el-option>
                    <el-option label="分组展示模式" value="grouped"></el-option>
                </el-select>
            </div>

            <div class="control-item">
                <el-button type="primary" icon="el-icon-plus" @click="addGroupRow">添加分组行</el-button>
                <el-button type="success" icon="el-icon-document-add" @click="addDataRow">添加数据行</el-button>
                <el-button type="info" icon="el-icon-download" @click="exportData">导出数据</el-button>
            </div>
        </el-card>

        <!-- 一维数组表格 -->
        <div v-if="displayMode === 'flat'" class="flat-table">
            <el-table :data="tableData" border stripe style="width: 100%" :row-class-name="getRowClassName">
                <el-table-column label="类型" width="80" align="center">
                    <template slot-scope="scope">
                        <el-tag :type="scope.row.type === 'group' ? 'primary' : 'success'" size="mini">
                            {{ scope.row.type === 'group' ? '分组' : '数据' }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="ID/分组名" width="120" prop="id">
                    <template slot-scope="scope">
                        {{ scope.row.type === 'group' ? scope.row.groupName : scope.row.id }}
                    </template>
                </el-table-column>
                <el-table-column label="名称/标题" width="150">
                    <template slot-scope="scope">
                        {{ scope.row.type === 'group' ? scope.row.title : scope.row.name }}
                    </template>
                </el-table-column>
                <el-table-column label="分类" width="120" prop="category">
                    <template slot-scope="scope">
                        {{ scope.row.category || '-' }}
                    </template>
                </el-table-column>
                <el-table-column label="店铺" width="120" prop="shop">
                    <template slot-scope="scope">
                        {{ scope.row.shop || '-' }}
                    </template>
                </el-table-column>
                <el-table-column label="价格" width="100" align="right">
                    <template slot-scope="scope">
                        <span v-if="scope.row.price" class="price">¥{{ scope.row.price }}</span>
                        <span v-else>-</span>
                    </template>
                </el-table-column>
                <el-table-column label="描述/地址" show-overflow-tooltip>
                    <template slot-scope="scope">
                        {{ scope.row.desc || scope.row.address || '-' }}
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="150" align="center">
                    <template slot-scope="scope">
                        <el-button size="mini" type="danger" icon="el-icon-delete"
                            @click="deleteItem(scope.$index)">删除</el-button>
                        <el-button size="mini" icon="el-icon-top" @click="moveUp(scope.$index)"
                            :disabled="scope.$index === 0">上移</el-button>
                        <el-button size="mini" icon="el-icon-bottom" @click="moveDown(scope.$index)"
                            :disabled="scope.$index === tableData.length - 1">下移</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <!-- 分组展示模式 -->
        <div v-else class="grouped-display">
            <el-collapse v-model="activeGroups" accordion>
                <el-collapse-item v-for="(group, groupIndex) in groupedDisplay" :key="groupIndex" :name="groupIndex">
                    <template slot="title">
                        <div class="group-header-content">
                            <el-tag type="primary" size="medium">{{ group.title }}</el-tag>
                            <el-badge :value="group.items.length" class="group-badge">
                                <span class="group-count-text">条记录</span>
                            </el-badge>
                            <div class="group-statistics" v-if="group.summary">
                                <span class="summary-text">平均价格: </span>
                                <el-tag type="warning" size="mini">¥{{ group.summary.avgPrice }}</el-tag>
                            </div>
                        </div>
                    </template>

                    <!-- 分组内容 -->
                    <el-table :data="group.items" border stripe style="width: 100%">
                        <el-table-column label="商品 ID" width="120" prop="id"></el-table-column>
                        <el-table-column label="商品名称" width="150" prop="name"></el-table-column>
                        <el-table-column label="分类" width="120" prop="category"></el-table-column>
                        <el-table-column label="店铺" width="120" prop="shop"></el-table-column>
                        <el-table-column label="价格" width="100" align="right">
                            <template slot-scope="scope">
                                <span class="price">¥{{ scope.row.price }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="描述" prop="desc" show-overflow-tooltip></el-table-column>
                        <el-table-column label="操作" width="100" align="center">
                            <template slot-scope="scope">
                                <el-button size="mini" type="danger" icon="el-icon-delete"
                                    @click="deleteDataItem(scope.row)">删除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-collapse-item>
            </el-collapse>
        </div>


    </div>
</template>

<script>
export default {
    name: 'FlatArrayTable',
    data() {
        return {
            displayMode: 'flat', // 'flat' 或 'grouped'
            groupCollapsed: {}, // 分组折叠状态
            activeGroups: [], // 激活的分组（用于 el-collapse）
            tableData: [
                // 分组1
                {
                    type: 'group',
                    groupName: 'group1',
                    title: '江浙小吃',
                    address: '上海市普陀区真北路'
                },
                // 分组1的数据
                {
                    type: 'item',
                    id: '12987122',
                    name: '好滋好味鸡蛋仔',
                    category: '江浙小吃',
                    desc: '荷兰优质淡奶，奶香浓而不腻',
                    address: '上海市普陀区真北路',
                    shop: '王小虎夫妻店',
                    shopId: '10333',
                    price: 15.8
                },
                {
                    type: 'item',
                    id: '12987125',
                    name: '蒸蛋羹',
                    category: '江浙小吃',
                    desc: '嫩滑蒸蛋，营养丰富',
                    address: '上海市普陀区真北路',
                    shop: '王小虎夫妻店',
                    shopId: '10333',
                    price: 12.0
                },
                // 分组2
                {
                    type: 'group',
                    groupName: 'group2',
                    title: '川湘菜',
                    address: '成都市锦江区春熙路'
                },
                // 分组2的数据
                {
                    type: 'item',
                    id: '12987123',
                    name: '麻辣小龙虾',
                    category: '川湘菜',
                    desc: '新鲜小龙虾，麻辣鲜香',
                    address: '上海市黄浦区南京路',
                    shop: '李记海鲜',
                    shopId: '10334',
                    price: 68.0
                },
                {
                    type: 'item',
                    id: '12987126',
                    name: '麻婆豆腐',
                    category: '川湘菜',
                    desc: '经典川菜，麻辣鲜香',
                    address: '成都市锦江区春熙路',
                    shop: '川味轩',
                    shopId: '10336',
                    price: 28.0
                },
                // 分组3
                {
                    type: 'group',
                    groupName: 'group3',
                    title: '京菜',
                    address: '北京市朝阳区建国路'
                },
                // 分组3的数据
                {
                    type: 'item',
                    id: '12987124',
                    name: '北京烤鸭',
                    category: '京菜',
                    desc: '正宗北京烤鸭，皮脆肉嫩',
                    address: '北京市朝阳区建国路',
                    shop: '全聚德',
                    shopId: '10335',
                    price: 128.0
                }
            ]
        }
    },
    computed: {
        // 分组展示数据
        groupedDisplay() {
            const groups = []
            let currentGroup = null

            this.tableData.forEach((item, index) => {
                if (item.type === 'group') {
                    if (currentGroup) {
                        groups.push(currentGroup)
                    }
                    currentGroup = {
                        ...item,
                        items: [],
                        collapsed: this.groupCollapsed[index] || false,
                        summary: null
                    }
                } else if (item.type === 'item' && currentGroup) {
                    currentGroup.items.push(item)
                }
            })

            if (currentGroup) {
                groups.push(currentGroup)
            }

            // 计算统计信息
            groups.forEach(group => {
                if (group.items.length > 0) {
                    const totalPrice = group.items.reduce((sum, item) => sum + (item.price || 0), 0)
                    group.summary = {
                        avgPrice: (totalPrice / group.items.length).toFixed(2),
                        totalPrice: totalPrice.toFixed(2),
                        count: group.items.length
                    }
                }
            })

            return groups
        }
    },
    methods: {
        // 处理显示模式变化
        handleModeChange() {
            // 可以在这里添加模式切换的逻辑
        },

        // 获取表格行的样式类名
        getRowClassName({ row }) {
            if (row.type === 'group') {
                return 'group-row'
            } else {
                return 'item-row'
            }
        },

        // 切换分组折叠状态
        toggleGroup(groupIndex) {
            this.$set(this.groupCollapsed, groupIndex, !this.groupCollapsed[groupIndex])
        },

        // 添加分组行
        addGroupRow() {
            const newGroup = {
                type: 'group',
                groupName: `group${Date.now()}`,
                title: '新分组',
                address: '新地址'
            }
            this.tableData.push(newGroup)
        },

        // 添加数据行
        addDataRow() {
            const newItem = {
                type: 'item',
                id: `${Date.now()}`,
                name: '新商品',
                category: '未分类',
                desc: '新商品描述',
                address: '新地址',
                shop: '新店铺',
                shopId: `${Date.now()}`,
                price: 0
            }
            this.tableData.push(newItem)
        },



        // 删除项目
        deleteItem(index) {
            // 方法已禁用，按钮保留但无功能
        },

        // 删除数据项目（分组模式下）
        deleteDataItem(item) {
            // 方法已禁用，按钮保留但无功能
        },

        // 上移
        moveUp(index) {
            // 方法已禁用，按钮保留但无功能
        },

        // 下移
        moveDown(index) {
            // 方法已禁用，按钮保留但无功能
        },

        // 导出数据
        exportData() {

        }
    }
}
</script>

<style scoped>
.flat-array-table {
    padding: 20px;
}

.demo-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px;
}

.demo-header h1 {
    margin: 0 0 10px 0;
    font-size: 28px;
    font-weight: 600;
}

.demo-header p {
    margin: 0;
    font-size: 16px;
    opacity: 0.9;
}

.controls {
    margin-bottom: 20px;
}

.controls .el-card {
    padding: 15px;
}

.controls .control-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.controls .control-item:last-child {
    margin-bottom: 0;
}

.control-label {
    font-weight: 500;
    color: #606266;
    font-size: 14px;
    min-width: 80px;
}

/* 表格行样式 */
.flat-table .el-table .group-row {
    background-color: #f0f9ff !important;
}

.flat-table .el-table .item-row {
    background-color: #fff !important;
}

/* 分组展示样式 */
.grouped-display {
    margin-top: 20px;
}

.group-header-content {
    display: flex;
    align-items: center;
    gap: 10px;
    width: 100%;
}

.group-badge {
    margin-left: auto;
}

.group-count-text {
    font-size: 12px;
    color: #909399;
}

.group-statistics {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-left: 10px;
}

.summary-text {
    font-size: 12px;
    color: #606266;
}

.price {
    font-weight: 600;
    color: #e6a23c;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .flat-array-table {
        padding: 10px;
    }

    .controls .control-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
}
</style>



<!-- {
    "tableData": [
        {
            // 分组1
        },
        {
            // 分组2
        },
        {
            "id": "12987122",
            "name": "好滋好味鸡蛋仔",
            "category": "江浙小吃",
            "desc": "荷兰优质淡奶，奶香浓而不腻",
            "address": "上海市普陀区真北路",
            "shop": "王小虎夫妻店",
            "shopId": "10333",
            "price": 15.8
        },
        {
            // 分组1
        },
        {
            // 分组2
        },
        {
            "id": "12987123",
            "name": "麻辣小龙虾",
            "category": "川湘菜",
            "desc": "新鲜小龙虾，麻辣鲜香",
            "address": "上海市黄浦区南京路",
            "shop": "李记海鲜",
            "shopId": "10334",
            "price": 68.0
        }
    ]
} -->