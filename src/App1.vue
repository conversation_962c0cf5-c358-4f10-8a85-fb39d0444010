<template>
    <el-table :data="tableData" border class="marking-table" :cell-class-name="cellClassName"
        :header-cell-class-name="headerCellClassName" @cell-mouse-enter="cellMouseEnter"
        @cell-mouse-leave="cellMouseLeave">
        <template v-for="column in tableColumn">
            <el-table-column :prop="column.prop" :label="column.label" />
        </template>
    </el-table>
</template>

<script>
export default {
    data() {
        return {
            hoverRowIndex: null,
            hoverColumnIndex: null,
            tableColumn: [
                {
                    label: "date",
                    prop: "date"
                },
                {
                    label: "name",
                    prop: "name"
                },
                {
                    label: "address",
                    prop: "address"
                }
            ],
            tableData: [{
                date: '2016-05-03',
                name: '<PERSON>',
                address: 'No. 189, Grove St, Los Angeles'
            }, {
                date: '2016-05-02',
                name: '<PERSON>',
                address: 'No. 189, Grove St, Los Angeles'
            }, {
                date: '2016-05-04',
                name: '<PERSON>',
                address: 'No. 189, Grove St, Los Angeles'
            }, {
                date: '2016-05-01',
                name: '<PERSON>',
                address: 'No. 189, Grove St, Los Angeles'
            }]
        }
    },
    methods: {
        cellClassName({ row, column, rowIndex, columnIndex }) {
            row.rowIndex = rowIndex
            column.columnIndex = columnIndex
            if (columnIndex === this.hoverColumnIndex && rowIndex === this.hoverRowIndex) {
                return 'select-row select-column'
            } else if (rowIndex === this.hoverRowIndex) {
                return 'select-row'
            } else if (columnIndex === this.hoverColumnIndex) {
                return 'select-column'
            }
        },
        headerCellClassName({ columnIndex }) {
            if (columnIndex === this.hoverColumnIndex) {
                return 'select-column'
            }
        },
        cellMouseEnter(row, column) {
            this.hoverRowIndex = row.rowIndex
            this.hoverColumnIndex = column.columnIndex
        },
        cellMouseLeave() {
            this.hoverRowIndex = null
            this.hoverColumnIndex = null
        }
    }
}
</script>

<style lang="scss" scoped>
.marking-table {
    :deep(.el-table__header-wrapper) {
        .el-table__header {
            th {
                &.select-column {
                    border-right: 1px solid #1989ff;
                }
            }
        }
    }

    :deep(.el-table__body-wrapper) {
        .el-table__body {
            .el-table__row {
                td {
                    &:hover {
                        border: 1px solid #1989ff;
                        background-color: rgba(25, 137, 255, 0.2); // 用 RGB
                    }

                    &.select-row {
                        border-bottom: 1px solid #1989ff;
                    }

                    &.select-column {
                        border-right: 1px solid #1989ff;
                    }
                }
            }
        }
    }
}
</style>