<template>
  <div id="app">
    <h1>多字段分组表格示例（无横向滚动）</h1>
    
    <!-- 使用分组表格组件 -->
    <GroupedTable :data="tableData" :group-fields="groupFields">
      <template #columns="{ data }">
        <el-table-column label="商品 ID" prop="id" width="120" fixed="left"></el-table-column>
        <el-table-column label="商品名称" prop="name" width="150" show-overflow-tooltip></el-table-column>
        <el-table-column label="分类" prop="category" width="120"></el-table-column>
        <el-table-column label="店铺" prop="shop" width="120"></el-table-column>
        <el-table-column label="价格" prop="price" width="100">
          <template slot-scope="scope">
            <span style="color: #f56c6c; font-weight: bold;">¥{{ scope.row.price }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="status" width="100">
          <template slot-scope="scope">
            <el-tag :type="getStatusType(scope.row.status)" size="small">{{ scope.row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="库存" prop="stock" width="80">
          <template slot-scope="scope">
            <span :style="{ color: scope.row.stock < 10 ? '#f56c6c' : '#67c23a' }">{{ scope.row.stock }}</span>
          </template>
        </el-table-column>
        <el-table-column label="销量" prop="sales" width="80"></el-table-column>
        <el-table-column label="评分" prop="rating" width="80">
          <template slot-scope="scope">
            <el-rate :value="scope.row.rating" disabled show-score text-color="#ff9900" score-template="{value}"></el-rate>
          </template>
        </el-table-column>
        <el-table-column label="供应商" prop="supplier" width="120" show-overflow-tooltip></el-table-column>
        <el-table-column label="产地" prop="origin" width="100"></el-table-column>
        <el-table-column label="保质期" prop="shelfLife" width="100"></el-table-column>
        <el-table-column label="重量(g)" prop="weight" width="80"></el-table-column>
        <el-table-column label="创建时间" prop="createTime" width="120">
          <template slot-scope="scope">
            <span>{{ formatDate(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="更新时间" prop="updateTime" width="120">
          <template slot-scope="scope">
            <span>{{ formatDate(scope.row.updateTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="标签" prop="tags" width="120">
          <template slot-scope="scope">
            <el-tag v-for="tag in scope.row.tags" :key="tag" size="mini" style="margin-right: 4px;">{{ tag }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="描述" prop="desc" show-overflow-tooltip></el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </template>
    </GroupedTable>

    <!-- 数据操作按钮 -->
    <div class="action-buttons">
      <el-button type="primary" @click="addRandomData">添加随机数据</el-button>
      <el-button @click="clearData">清空数据</el-button>
      <el-button @click="resetData">重置数据</el-button>
    </div>
  </div>
</template>

<script>
import GroupedTable from './components/GroupedTable.vue'

export default {
  name: 'App',
  components: {
    GroupedTable
  },
  data() {
    return {
      // 分组字段配置
      groupFields: [
        { label: '分类', value: 'category' },
        { label: '店铺', value: 'shop' },
        { label: '地址', value: 'address' },
        { label: '状态', value: 'status' },
        { label: '价格区间', value: 'priceRange' },
        { label: '供应商', value: 'supplier' },
        { label: '产地', value: 'origin' },
        { label: '保质期', value: 'shelfLife' },
        { label: '库存状态', value: 'stockStatus' }
      ],
      // 表格数据
      tableData: [
        {
          id: '12987122',
          name: '好滋好味鸡蛋仔',
          category: '江浙小吃',
          desc: '荷兰优质淡奶，奶香浓而不腻',
          address: '上海市普陀区',
          shop: '王小虎夫妻店',
          price: 25.8,
          status: '在售',
          priceRange: '20-30元',
          stock: 45,
          sales: 128,
          rating: 4.5,
          supplier: '上海美食供应商',
          origin: '上海',
          shelfLife: '7天',
          weight: 150,
          createTime: new Date('2024-01-15'),
          updateTime: new Date('2024-08-01'),
          tags: ['热销', '推荐']
        },
        {
          id: '12987123',
          name: '麻辣小龙虾',
          category: '川湘菜',
          desc: '新鲜小龙虾，麻辣鲜香',
          address: '上海市黄浦区',
          shop: '李记海鲜',
          price: 68.0,
          status: '在售',
          priceRange: '60-80元',
          stock: 23,
          sales: 89,
          rating: 4.8,
          supplier: '湖北水产供应商',
          origin: '湖北',
          shelfLife: '1天',
          weight: 500,
          createTime: new Date('2024-02-10'),
          updateTime: new Date('2024-08-02'),
          tags: ['新鲜', '麻辣'],
          stockStatus: '正常'
        },
        {
          id: '12987124',
          name: '北京烤鸭',
          category: '京菜',
          desc: '正宗北京烤鸭，皮脆肉嫩',
          address: '北京市朝阳区',
          shop: '全聚德',
          price: 128.0,
          status: '缺货',
          priceRange: '100元以上',
          stock: 0,
          sales: 56,
          rating: 4.9,
          supplier: '北京传统食品厂',
          origin: '北京',
          shelfLife: '3天',
          weight: 1200,
          createTime: new Date('2024-01-20'),
          updateTime: new Date('2024-07-30'),
          tags: ['传统', '精品'],
          stockStatus: '缺货'
        },
        {
          id: '12987125',
          name: '蒸蛋羹',
          category: '江浙小吃',
          desc: '嫩滑蒸蛋，营养丰富',
          address: '上海市普陀区',
          shop: '王小虎夫妻店',
          price: 15.0,
          status: '在售',
          priceRange: '10-20元',
          stock: 67,
          sales: 234,
          rating: 4.3,
          supplier: '上海美食供应商',
          origin: '上海',
          shelfLife: '2天',
          weight: 200,
          createTime: new Date('2024-03-05'),
          updateTime: new Date('2024-08-03'),
          tags: ['营养', '健康'],
          stockStatus: '充足'
        },
        {
          id: '12987126',
          name: '麻婆豆腐',
          category: '川湘菜',
          desc: '经典川菜，麻辣鲜香',
          address: '成都市锦江区',
          shop: '川味轩',
          price: 32.0,
          status: '在售',
          priceRange: '30-40元',
          stock: 34,
          sales: 167,
          rating: 4.6,
          supplier: '四川调料厂',
          origin: '四川',
          shelfLife: '当天',
          weight: 300,
          createTime: new Date('2024-02-20'),
          updateTime: new Date('2024-08-04'),
          tags: ['经典', '川菜'],
          stockStatus: '正常'
        },
        {
          id: '12987127',
          name: '宫保鸡丁',
          category: '川湘菜',
          desc: '传统川菜，酸甜可口',
          address: '上海市黄浦区',
          shop: '李记海鲜',
          price: 38.0,
          status: '停售',
          priceRange: '30-40元',
          stock: 0,
          sales: 78,
          rating: 4.4,
          supplier: '四川调料厂',
          origin: '四川',
          shelfLife: '当天',
          weight: 350,
          createTime: new Date('2024-01-30'),
          updateTime: new Date('2024-07-25'),
          tags: ['传统', '停售'],
          stockStatus: '缺货'
        },
        {
          id: '12987128',
          name: '小笼包',
          category: '江浙小吃',
          desc: '皮薄馅大，汤汁鲜美',
          address: '上海市黄浦区',
          shop: '南翔小笼',
          price: 22.0,
          status: '在售',
          priceRange: '20-30元',
          stock: 89,
          sales: 345,
          rating: 4.7,
          supplier: '江南面点厂',
          origin: '江苏',
          shelfLife: '当天',
          weight: 120,
          createTime: new Date('2024-01-10'),
          updateTime: new Date('2024-08-05'),
          tags: ['特色', '热销'],
          stockStatus: '充足'
        },
        {
          id: '12987129',
          name: '水煮鱼',
          category: '川湘菜',
          desc: '鱼肉鲜嫩，麻辣过瘾',
          address: '成都市锦江区',
          shop: '川味轩',
          price: 58.0,
          status: '缺货',
          priceRange: '50-60元',
          stock: 5,
          sales: 123,
          rating: 4.8,
          supplier: '四川水产供应商',
          origin: '四川',
          shelfLife: '1天',
          weight: 800,
          createTime: new Date('2024-02-15'),
          updateTime: new Date('2024-07-28'),
          tags: ['麻辣', '鲜美'],
          stockStatus: '紧张'
        }
      ]
    }
  },
  methods: {
    // 获取状态标签类型
    getStatusType(status) {
      const statusMap = {
        '在售': 'success',
        '缺货': 'warning',
        '停售': 'danger'
      }
      return statusMap[status] || 'info'
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return '-'
      const d = new Date(date)
      return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`
    },
    
    // 设置分组字段（用于快速测试）
    setGroupFields(fields) {
      // 通过 ref 访问子组件并设置分组字段
      this.$refs.groupedTable?.setSelectedGroupFields?.(fields) || 
      this.$message.info(`请手动选择分组字段: ${fields.map(f => this.groupFields.find(gf => gf.value === f)?.label).join(', ')}`)
    },
    
    // 编辑操作
    handleEdit(row) {
      this.$message.info(`编辑商品: ${row.name}`)
    },
    
    // 删除操作
    handleDelete(row) {
      this.$confirm(`确定删除商品 ${row.name} 吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.tableData.findIndex(item => item.id === row.id)
        if (index > -1) {
          this.tableData.splice(index, 1)
          this.$message.success('删除成功!')
        }
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },
    
    // 添加随机数据
    addRandomData() {
      const categories = ['江浙小吃', '川湘菜', '京菜', '粤菜', '东北菜']
      const shops = ['王小虎夫妻店', '李记海鲜', '全聚德', '川味轩', '南翔小笼', '老北京炸酱面']
      const addresses = ['上海市普陀区', '上海市黄浦区', '北京市朝阳区', '成都市锦江区', '广州市天河区']
      const statuses = ['在售', '缺货', '停售']
      const names = ['红烧肉', '糖醋排骨', '鱼香肉丝', '回锅肉', '白切鸡', '蒜蓉菜心']
      const suppliers = ['上海美食供应商', '湖北水产供应商', '北京传统食品厂', '四川调料厂', '江南面点厂']
      const origins = ['上海', '湖北', '北京', '四川', '江苏', '广东']
      const shelfLives = ['当天', '1天', '2天', '3天', '7天', '15天']
      const tagOptions = [['热销', '推荐'], ['新鲜', '麻辣'], ['传统', '精品'], ['营养', '健康'], ['经典', '川菜'], ['特色', '热销']]

      const price = Math.floor(Math.random() * 100) + 10
      const stock = Math.floor(Math.random() * 100)
      const randomItem = {
        id: Date.now().toString(),
        name: names[Math.floor(Math.random() * names.length)],
        category: categories[Math.floor(Math.random() * categories.length)],
        desc: '美味可口，值得品尝',
        address: addresses[Math.floor(Math.random() * addresses.length)],
        shop: shops[Math.floor(Math.random() * shops.length)],
        price: price,
        status: statuses[Math.floor(Math.random() * statuses.length)],
        stock: stock,
        sales: Math.floor(Math.random() * 500),
        rating: (Math.random() * 2 + 3).toFixed(1), // 3.0-5.0
        supplier: suppliers[Math.floor(Math.random() * suppliers.length)],
        origin: origins[Math.floor(Math.random() * origins.length)],
        shelfLife: shelfLives[Math.floor(Math.random() * shelfLives.length)],
        weight: Math.floor(Math.random() * 1000) + 100,
        createTime: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000), // 随机过去一年内的日期
        updateTime: new Date(),
        tags: tagOptions[Math.floor(Math.random() * tagOptions.length)],
        stockStatus: stock === 0 ? '缺货' : stock < 10 ? '紧张' : stock < 50 ? '正常' : '充足'
      }
      
      // 根据价格设置价格区间
      if (randomItem.price < 20) {
        randomItem.priceRange = '10-20元'
      } else if (randomItem.price < 30) {
        randomItem.priceRange = '20-30元'
      } else if (randomItem.price < 40) {
        randomItem.priceRange = '30-40元'
      } else if (randomItem.price < 60) {
        randomItem.priceRange = '50-60元'
      } else if (randomItem.price < 80) {
        randomItem.priceRange = '60-80元'
      } else {
        randomItem.priceRange = '100元以上'
      }
      
      this.tableData.push(randomItem)
      this.$message.success('添加成功!')
    },
    
    // 清空数据
    clearData() {
      this.$confirm('确定清空所有数据吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.tableData = []
        this.$message.success('清空成功!')
      })
    },
    
    // 重置数据
    resetData() {
      location.reload()
    }
  }
}
</script>

<style>
#app {
  font-family: 'Avenir', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
  margin: 20px;
}

h1 {
  text-align: center;
  color: #409eff;
  margin-bottom: 30px;
}

.demo-table-expand {
  font-size: 0;
}

.demo-table-expand label {
  width: 90px;
  color: #99a9bf;
}

.demo-table-expand .el-form-item {
  margin-right: 0;
  margin-bottom: 0;
  width: 50%;
}

.feature-description {
  margin: 30px 0;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.feature-description h3 {
  color: #409eff;
  margin-bottom: 15px;
}

.feature-description ul {
  margin-bottom: 20px;
}

.feature-description li {
  margin-bottom: 8px;
  line-height: 1.6;
}

.group-suggestions {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.group-suggestions .el-button {
  margin: 0;
}

.action-buttons {
  margin-top: 20px;
  text-align: center;
}

.action-buttons .el-button {
  margin: 0 10px;
}

.level-colors {
  margin-top: 15px;
}

.level-colors h4 {
  color: #409eff;
  margin-bottom: 10px;
}

.color-item {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.color-box {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  margin-right: 10px;
  display: inline-block;
}

.color-box.level-0 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.color-box.level-1 {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.color-box.level-2 {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.color-box.level-3 {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

@media (max-width: 768px) {
  .group-suggestions {
    flex-direction: column;
  }

  .group-suggestions .el-button {
    width: 100%;
  }

  .level-colors {
    margin-top: 20px;
  }

  .color-item {
    margin-bottom: 8px;
  }
}
</style>
