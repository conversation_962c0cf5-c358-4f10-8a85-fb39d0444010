import Vue from 'vue'
// import App from './App.vue'
// import App from './App1.vue'
// import App from './App2.vue'
// import App from './App3.vue'
// import App from './App4.vue'
// import App from './components/GroupedTableDemo.vue'
import App from './components/FlatArrayTable.vue'
import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';

Vue.config.productionTip = false
Vue.use(ElementUI);

new Vue({
  render: h => h(App),
}).$mount('#app')
