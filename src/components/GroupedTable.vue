<template>
  <div class="grouped-table-wrapper">
    <!-- 分组控制面板 -->
    <div class="group-control-panel">
      <div class="control-row">
        <div class="control-item">
          <label>分组字段:</label>
          <el-select v-model="selectedGroupFields" placeholder="选择分组字段（可多选）" @change="handleGroupChange" multiple
            clearable collapse-tags style="min-width: 300px;">
            <el-option v-for="field in groupFields" :key="field.value" :label="field.label"
              :value="field.value"></el-option>
          </el-select>
        </div>

        <div class="control-item" v-if="selectedGroupFields.length > 0">
          <el-button size="small" @click="expandAll">全部展开</el-button>
          <el-button size="small" @click="collapseAll">全部折叠</el-button>
        </div>
      </div>
    </div>

    <!-- 分组表格内容 -->
    <div v-if="selectedGroupFields.length > 0" class="grouped-content">
      <!-- 统一滚动容器 -->
      <div class="unified-scroll-container" @scroll="handleScroll" ref="scrollContainer">
        <div class="table-content-wrapper" :style="{ minWidth: tableMinWidth + 'px' }">
          <!-- 表头（分组模式下显示在顶部） -->
          <div class="grouped-table-header">
            <el-table class="my-table header-table" :data="[]" size="small" ref="headerTable">
              <slot name="columns" :data="[]"></slot>
            </el-table>
          </div>

          <!-- 递归渲染嵌套分组 -->
          <div class="grouped-table-body">
            <div v-for="(groupData, groupKey) in groupedData" :key="groupKey">
              <nested-group :group-data="groupData" :group-key="groupKey" :level="0" :group-fields="selectedGroupFields"
                :table-min-width="tableMinWidth" @toggle-group="toggleGroup">
                <template #table="{ items }">
                  <el-table :data="items" size="small" :show-header="false" class="body-table">
                    <slot name="columns" :data="items"></slot>
                  </el-table>
                </template>
              </nested-group>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 普通表格（无分组） -->
    <div v-else class="normal-table">
      <div class="unified-scroll-container">
        <el-table :data="data" style="width: 100%">
          <slot name="columns" :data="data"></slot>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import NestedGroup from './NestedGroup.vue'

export default {
  name: 'GroupedTable',
  components: {
    NestedGroup
  },
  props: {
    data: {
      type: Array,
      default: () => []
    },
    groupFields: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      selectedGroupFields: [], // 选中的分组字段数组
      groupCollapsed: {},
      tableMinWidth: 1200 // 表格最小宽度，确保所有列都能显示
    }
  },
  computed: {
    // 分组后的数据（嵌套结构）
    groupedData() {
      if (!this.selectedGroupFields.length) return {}

      return this.buildNestedGroups(this.data, this.selectedGroupFields, 0)
    }
  },
  methods: {
    // 处理分组变化
    handleGroupChange() {
      this.groupCollapsed = {}
    },

    // 构建嵌套分组数据
    buildNestedGroups(data, groupFields, level) {
      if (level >= groupFields.length) {
        // 已经到达最后一层，返回数据项
        return data
      }

      const currentField = groupFields[level]
      const groups = {}

      data.forEach(item => {
        const groupValue = item[currentField] || '未分类'

        if (!groups[groupValue]) {
          groups[groupValue] = []
        }

        groups[groupValue].push(item)
      })

      // 递归处理下一层分组
      const result = {}
      Object.keys(groups).forEach(groupKey => {
        const groupPath = this.generateGroupPath(groupFields, level, groupKey)

        if (level === groupFields.length - 1) {
          // 最后一层，直接存储数据
          result[groupKey] = {
            items: groups[groupKey],
            collapsed: this.groupCollapsed[groupPath] || false,
            isLeaf: true,
            level: level,
            fieldName: currentField
          }
        } else {
          // 不是最后一层，继续递归
          result[groupKey] = {
            subGroups: this.buildNestedGroups(groups[groupKey], groupFields, level + 1),
            collapsed: this.groupCollapsed[groupPath] || false,
            isLeaf: false,
            level: level,
            fieldName: currentField,
            totalItems: groups[groupKey].length
          }
        }
      })

      return result
    },

    // 生成分组路径（用于折叠状态管理）
    generateGroupPath(groupFields, level, groupKey) {
      return `${groupFields.slice(0, level + 1).join('_')}_${groupKey}`
    },

    // 获取字段标签
    getFieldLabel(fieldValue) {
      const field = this.groupFields.find(f => f.value === fieldValue)
      return field ? field.label.replace('按', '').replace('分组', '') : fieldValue
    },

    // 切换分组折叠状态
    toggleGroup(groupPath) {
      this.$set(this.groupCollapsed, groupPath, !this.groupCollapsed[groupPath])
    },

    // 全部展开
    expandAll() {
      const newCollapsed = {}
      Object.keys(this.groupCollapsed).forEach(key => {
        newCollapsed[key] = false
      })
      this.groupCollapsed = newCollapsed
    },

    // 全部折叠
    collapseAll() {
      const newCollapsed = {}
      Object.keys(this.groupCollapsed).forEach(key => {
        newCollapsed[key] = true
      })
      this.groupCollapsed = newCollapsed
    },

    // 处理统一滚动
    handleScroll(event) {
      const scrollLeft = event.target.scrollLeft

      // 同步所有表格的滚动位置
      this.syncTableScroll(scrollLeft)
    },

    // 同步表格滚动位置
    syncTableScroll(scrollLeft) {
      // 同步表头滚动
      if (this.$refs.headerTable && this.$refs.headerTable.$el) {
        const headerBodyWrapper = this.$refs.headerTable.$el.querySelector('.el-table__body-wrapper')
        if (headerBodyWrapper) {
          headerBodyWrapper.scrollLeft = scrollLeft
        }
        const headerHeaderWrapper = this.$refs.headerTable.$el.querySelector('.el-table__header-wrapper')
        if (headerHeaderWrapper) {
          headerHeaderWrapper.scrollLeft = scrollLeft
        }
      }

      // 同步所有分组表格的滚动
      const bodyTables = this.$el.querySelectorAll('.body-table .el-table__body-wrapper')
      bodyTables.forEach(wrapper => {
        wrapper.scrollLeft = scrollLeft
      })
    },

    // 计算表格最小宽度
    calculateTableMinWidth() {
      // 延迟执行，确保DOM完全渲染
      setTimeout(() => {
        // // 方法1: 通过实际DOM计算
        // if (this.calculateWidthFromDOM()) {
        //   return
        // }

        // 方法2: 通过列配置计算
        this.calculateWidthFromSlot()
      }, 100)
    },

    // // 通过DOM元素计算实际宽度
    // calculateWidthFromDOM() {
    //   const headerTable = this.$refs.headerTable
    //   if (headerTable && headerTable.$el) {
    //     // 尝试获取表头的实际宽度
    //     const headerWrapper = headerTable.$el.querySelector('.el-table__header-wrapper')
    //     const headerTable_el = headerTable.$el.querySelector('.el-table__header table')

    //     if (headerTable_el) {
    //       // 获取表格的自然宽度
    //       const tableWidth = headerTable_el.offsetWidth
    //       if (tableWidth > 0) {
    //         this.tableMinWidth = Math.max(tableWidth + 20, 1200) // 加20px余量
    //         console.log('通过DOM计算表格宽度:', this.tableMinWidth)
    //         return true
    //       }
    //     }

    //     // 尝试通过列头计算
    //     const thElements = headerTable.$el.querySelectorAll('.el-table__header th')
    //     if (thElements.length > 0) {
    //       let totalWidth = 0
    //       thElements.forEach(th => {
    //         totalWidth += th.offsetWidth
    //       })
    //       if (totalWidth > 0) {
    //         this.tableMinWidth = Math.max(totalWidth + 20, 1200)
    //         console.log('通过列头计算表格宽度:', this.tableMinWidth)
    //         return true
    //       }
    //     }
    //   }
    //   return false
    // },

    // 通过分析slot内容计算宽度
    calculateWidthFromSlot() {
      // 获取slot中的列配置
      const columnWidths = this.getColumnWidths()

      if (columnWidths.length > 0) {
        // 计算所有列宽的总和
        const totalWidth = columnWidths.reduce((sum, width) => sum + width, 0)
        this.tableMinWidth = Math.max(totalWidth, 1200) // 最小1200px
      } else {
        // 如果无法获取列宽，使用默认计算
        this.tableMinWidth = 1500
      }
    },

    // 获取列宽配置
    getColumnWidths() {
      // 尝试从父组件的slot中获取列配置
      try {
        // 这里需要分析slot内容，获取每列的宽度
        // 由于slot是动态的，我们使用一个更通用的方法

        // 预定义的列宽映射（基于App4.vue中的配置）
        const columnConfig = [
          { prop: 'expand', width: 50 },      // 展开列
          { prop: 'id', width: 120 },         // 商品ID
          { prop: 'name', width: 150 },       // 商品名称
          { prop: 'category', width: 120 },   // 分类
          { prop: 'shop', width: 120 },       // 店铺
          { prop: 'price', width: 100 },      // 价格
          { prop: 'status', width: 100 },     // 状态
          { prop: 'stock', width: 80 },       // 库存
          { prop: 'sales', width: 80 },       // 销量
          { prop: 'rating', width: 80 },      // 评分
          { prop: 'supplier', width: 120 },   // 供应商
          { prop: 'origin', width: 100 },     // 产地
          { prop: 'shelfLife', width: 100 },  // 保质期
          { prop: 'weight', width: 80 },      // 重量
          { prop: 'createTime', width: 120 }, // 创建时间
          { prop: 'updateTime', width: 120 }, // 更新时间
          { prop: 'tags', width: 120 },       // 标签
          { prop: 'desc', width: 200 },       // 描述（自适应列，给个默认值）
          { prop: 'actions', width: 150 }     // 操作
        ]

        return columnConfig.map(col => col.width)
      } catch (error) {
        console.warn('无法获取列宽配置，使用默认值', error)
        return []
      }
    }
  },

  mounted() {
    // 计算表格最小宽度
    this.$nextTick(() => {
      this.calculateTableMinWidth()
    })
  },

  watch: {
    // 监听分组字段变化，重新计算宽度
    selectedGroupFields: {
      handler() {
        this.$nextTick(() => {
          this.calculateTableMinWidth()
        })
      },
      deep: true
    },

    // 监听数据变化，重新计算宽度
    data: {
      handler() {
        this.$nextTick(() => {
          this.calculateTableMinWidth()
        })
      },
      deep: true
    }
  }
}
</script>

<style scoped>
/* 统一滚动容器 */
.unified-scroll-container {
  overflow-x: auto;
  overflow-y: visible;
  width: 100%;
}

.table-content-wrapper {
  width: 100%;
}

/* 隐藏表头表格的body部分 */
:deep(.my-table .el-table__body-wrapper) {
  display: none !important;
}

/* 禁用所有子表格的横向滚动 */
:deep(.body-table .el-table__body-wrapper) {
  overflow-x: hidden !important;
  /* width: 500px; */
}

:deep(.body-table .el-table__header-wrapper) {
  overflow-x: hidden !important;
}

/* 确保表格内容不会超出容器 */
:deep(.el-table) {
  table-layout: fixed;
}

:deep(.el-table .el-table__cell) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.grouped-table-wrapper {
  padding: 20px;
}

.group-control-panel {
  background-color: #f5f7fa;
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 20px;
}

.control-row {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.control-row:last-child {
  margin-bottom: 0;
}

.control-item {
  display: flex;
  align-items: center;
  margin-right: 20px;
}

.control-item label {
  margin-right: 8px;
  font-weight: 500;
  color: #606266;
  white-space: nowrap;
}

.grouped-content {
  border: 1px solid #ebeef5;
  border-radius: 6px;
  overflow: visible;
}

.primary-group {
  border-bottom: 1px solid #ebeef5;
}

.primary-group:last-child {
  border-bottom: none;
}

/* GroupedTable 特有样式 */
.grouped-table-header {
  background-color: #fff;
  border: 1px solid #ebeef5;
  border-bottom: none;
  border-radius: 6px 6px 0 0;
}

.grouped-table-header .el-table {
  border: none;
  border-radius: 6px 6px 0 0;
}

.grouped-table-header .el-table::before {
  display: none;
}

.grouped-table-header .el-table th {
  background-color: #fafafa;
  border-bottom: 2px solid #e4e7ed;
  font-weight: 600;
}

.grouped-table-body {
  border: 1px solid #ebeef5;
  border-top: none;
  border-radius: 0 0 6px 6px;
  overflow: visible;
}

/* 确保分组内容的表格与表头对齐 */
.grouped-table-body .el-table {
  border: none;
}

.grouped-table-body .el-table::before {
  display: none;
}

.grouped-table-body .el-table td {
  border-bottom: 1px solid #f0f0f0;
}

.normal-table {
  margin-top: 20px;
}

/* 表格样式优化 */
.primary-group-content .el-table,
.secondary-group-content .el-table {
  border: none;
}

.primary-group-content .el-table::before,
.secondary-group-content .el-table::before {
  display: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .control-row {
    flex-direction: column;
    align-items: flex-start;
  }

  .control-item {
    margin-right: 0;
    margin-bottom: 8px;
    width: 100%;
  }

  .control-item:last-child {
    margin-bottom: 0;
  }
}
</style>
