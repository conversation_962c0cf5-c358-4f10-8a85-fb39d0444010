<template>
  <div class="nested-group" :class="'level-' + level">
    <!-- 分组标题 -->
    <div
      class="group-header"
      :style="getHeaderStyle()"
      @click="handleToggle"
      @mouseenter="isHovered = true"
      @mouseleave="isHovered = false"
    >
      <i :class="groupData.collapsed ? 'el-icon-arrow-right' : 'el-icon-arrow-down'"></i>
      <span class="group-title">{{ getFieldLabel(groupData.fieldName) }}: {{ groupKey }}</span>
      <span class="group-count">({{ getItemCount() }}项)</span>
      <div class="group-statistics">
        <span>{{ getFieldLabel(groupData.fieldName) }}: {{ getItemCount() }} 条记录</span>
      </div>
    </div>
    
    <!-- 分组内容 -->
    <div v-show="!groupData.collapsed" class="group-content" :class="'level-' + level + '-content'">
      <!-- 如果是叶子节点，显示表格 -->
      <div v-if="groupData.isLeaf" class="group-table">
        <slot name="table" :items="groupData.items"></slot>
      </div>
      
      <!-- 如果不是叶子节点，递归渲染子分组 -->
      <div v-else class="sub-groups">
        <nested-group
          v-for="(subGroupData, subGroupKey) in groupData.subGroups"
          :key="subGroupKey"
          :group-data="subGroupData"
          :group-key="subGroupKey"
          :level="level + 1"
          :group-fields="groupFields"
          :table-min-width="tableMinWidth"
          @toggle-group="$emit('toggle-group', $event)"
        >
          <template #table="{ items }">
            <slot name="table" :items="items"></slot>
          </template>
        </nested-group>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'NestedGroup',
  data() {
    return {
      isHovered: false
    }
  },
  props: {
    groupData: {
      type: Object,
      required: true
    },
    groupKey: {
      type: String,
      required: true
    },
    level: {
      type: Number,
      required: true
    },
    groupFields: {
      type: Array,
      required: true
    },
    tableMinWidth: {
      type: Number,
      default: 1200
    }
  },
  methods: {
    handleToggle() {
      const groupPath = this.generateGroupPath()
      this.$emit('toggle-group', groupPath)
    },

    generateGroupPath() {
      // 直接在这里生成路径，避免依赖$parent
      return `${this.groupFields.slice(0, this.level + 1).join('_')}_${this.groupKey}`
    },
    
    getFieldLabel(fieldValue) {
      // 直接在这里处理字段标签，避免依赖$parent
      // 简单的标签处理逻辑
      const labelMap = {
        'category': '分类',
        'shop': '店铺',
        'address': '地址',
        'status': '状态',
        'priceRange': '价格区间',
        'supplier': '供应商',
        'origin': '产地',
        'shelfLife': '保质期',
        'stockStatus': '库存状态'
      }
      return labelMap[fieldValue] || fieldValue
    },

    // 根据层级计算 header 样式
    getHeaderStyle() {
      // 基础 padding
      const basePadding = 16
      // 每层级增加的 padding
      const levelPadding = 16

      // 计算左侧 padding（根据层级递增）
      const leftPadding = basePadding + (this.level * levelPadding)
      // 右侧 padding 保持固定
      const rightPadding = basePadding

      // 根据层级获取背景色
      const { background, hoverBackground } = this.getBackgroundColors()

      return {
        paddingLeft: `${leftPadding}px`,
        paddingRight: `${rightPadding}px`,
        paddingTop: this.level === 0 ? '14px' : '12px',
        paddingBottom: this.level === 0 ? '14px' : '12px',
        background: this.isHovered ? hoverBackground : background,
        color: 'white',
        cursor: 'pointer',
        display: 'flex',
        alignItems: 'center',
        transition: 'all 0.3s'
      }
    },

    // 根据层级获取背景色 - 100种预定义颜色
    getBackgroundColors() {
      const colorSchemes = [
        // 0-9: 蓝色系列
        { background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', hoverBackground: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)' },
        { background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)', hoverBackground: 'linear-gradient(135deg, #3e9bfd 0%, #00e1fd 100%)' },
        { background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)', hoverBackground: 'linear-gradient(135deg, #2dd4bf 0%, #06b6d4 100%)' },
        { background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)', hoverBackground: 'linear-gradient(135deg, #f9618b 0%, #fdd831 100%)' },
        { background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)', hoverBackground: 'linear-gradient(135deg, #96e6e1 0%, #fcc9d6 100%)' },
        { background: 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)', hoverBackground: 'linear-gradient(135deg, #ff8a95 0%, #fdbde2 100%)' },
        { background: 'linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%)', hoverBackground: 'linear-gradient(135deg, #9575cd 0%, #f8bbd9 100%)' },
        { background: 'linear-gradient(135deg, #fad0c4 0%, #ffd1ff 100%)', hoverBackground: 'linear-gradient(135deg, #f9c5b8 0%, #ffbfff 100%)' },
        { background: 'linear-gradient(135deg, #ff9a56 0%, #ffad56 100%)', hoverBackground: 'linear-gradient(135deg, #ff8a40 0%, #ff9d40 100%)' },
        { background: 'linear-gradient(135deg, #a8e6cf 0%, #dcedc1 100%)', hoverBackground: 'linear-gradient(135deg, #98dcc0 0%, #d1e7b5 100%)' },

        // 10-19: 紫色系列
        { background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', hoverBackground: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)' },
        { background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)', hoverBackground: 'linear-gradient(135deg, #e084ea 0%, #e4485b 100%)' },
        { background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)', hoverBackground: 'linear-gradient(135deg, #3e9bfd 0%, #00e1fd 100%)' },
        { background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)', hoverBackground: 'linear-gradient(135deg, #2dd4bf 0%, #06b6d4 100%)' },
        { background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)', hoverBackground: 'linear-gradient(135deg, #f9618b 0%, #fdd831 100%)' },
        { background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)', hoverBackground: 'linear-gradient(135deg, #96e6e1 0%, #fcc9d6 100%)' },
        { background: 'linear-gradient(135deg, #d299c2 0%, #fef9d7 100%)', hoverBackground: 'linear-gradient(135deg, #c788b1 0%, #fdf6c8 100%)' },
        { background: 'linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%)', hoverBackground: 'linear-gradient(135deg, #7ae7f5 0%, #5596f6 100%)' },
        { background: 'linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%)', hoverBackground: 'linear-gradient(135deg, #fcb01e 0%, #1fb5b7 100%)' },
        { background: 'linear-gradient(135deg, #ee9ca7 0%, #ffdde1 100%)', hoverBackground: 'linear-gradient(135deg, #eb8c98 0%, #ffd1d6 100%)' },

        // 20-29: 绿色系列
        { background: 'linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%)', hoverBackground: 'linear-gradient(135deg, #4a9628 0%, #98dcc0 100%)' },
        { background: 'linear-gradient(135deg, #11998e 0%, #38ef7d 100%)', hoverBackground: 'linear-gradient(135deg, #0e8a7f 0%, #2dd865 100%)' },
        { background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', hoverBackground: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)' },
        { background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)', hoverBackground: 'linear-gradient(135deg, #e084ea 0%, #e4485b 100%)' },
        { background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)', hoverBackground: 'linear-gradient(135deg, #3e9bfd 0%, #00e1fd 100%)' },
        { background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)', hoverBackground: 'linear-gradient(135deg, #2dd4bf 0%, #06b6d4 100%)' },
        { background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)', hoverBackground: 'linear-gradient(135deg, #f9618b 0%, #fdd831 100%)' },
        { background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)', hoverBackground: 'linear-gradient(135deg, #96e6e1 0%, #fcc9d6 100%)' },
        { background: 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)', hoverBackground: 'linear-gradient(135deg, #ff8a95 0%, #fdbde2 100%)' },
        { background: 'linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%)', hoverBackground: 'linear-gradient(135deg, #9575cd 0%, #f8bbd9 100%)' },

        // 30-39: 橙色系列
        { background: 'linear-gradient(135deg, #ff6b6b 0%, #feca57 100%)', hoverBackground: 'linear-gradient(135deg, #ff5252 0%, #fdc43f 100%)' },
        { background: 'linear-gradient(135deg, #ff9068 0%, #fd746c 100%)', hoverBackground: 'linear-gradient(135deg, #ff7f50 0%, #fc6254 100%)' },
        { background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', hoverBackground: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)' },
        { background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)', hoverBackground: 'linear-gradient(135deg, #e084ea 0%, #e4485b 100%)' },
        { background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)', hoverBackground: 'linear-gradient(135deg, #3e9bfd 0%, #00e1fd 100%)' },
        { background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)', hoverBackground: 'linear-gradient(135deg, #2dd4bf 0%, #06b6d4 100%)' },
        { background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)', hoverBackground: 'linear-gradient(135deg, #f9618b 0%, #fdd831 100%)' },
        { background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)', hoverBackground: 'linear-gradient(135deg, #96e6e1 0%, #fcc9d6 100%)' },
        { background: 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)', hoverBackground: 'linear-gradient(135deg, #ff8a95 0%, #fdbde2 100%)' },
        { background: 'linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%)', hoverBackground: 'linear-gradient(135deg, #9575cd 0%, #f8bbd9 100%)' },

        // 40-49: 红色系列
        { background: 'linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%)', hoverBackground: 'linear-gradient(135deg, #ff2d5a 0%, #ff3819 100%)' },
        { background: 'linear-gradient(135deg, #ff5f6d 0%, #ffc371 100%)', hoverBackground: 'linear-gradient(135deg, #ff4c5e 0%, #ffb85f 100%)' },
        { background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', hoverBackground: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)' },
        { background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)', hoverBackground: 'linear-gradient(135deg, #e084ea 0%, #e4485b 100%)' },
        { background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)', hoverBackground: 'linear-gradient(135deg, #3e9bfd 0%, #00e1fd 100%)' },
        { background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)', hoverBackground: 'linear-gradient(135deg, #2dd4bf 0%, #06b6d4 100%)' },
        { background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)', hoverBackground: 'linear-gradient(135deg, #f9618b 0%, #fdd831 100%)' },
        { background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)', hoverBackground: 'linear-gradient(135deg, #96e6e1 0%, #fcc9d6 100%)' },
        { background: 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)', hoverBackground: 'linear-gradient(135deg, #ff8a95 0%, #fdbde2 100%)' },
        { background: 'linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%)', hoverBackground: 'linear-gradient(135deg, #9575cd 0%, #f8bbd9 100%)' },

        // 50-59: 青色系列
        { background: 'linear-gradient(135deg, #00c6ff 0%, #0072ff 100%)', hoverBackground: 'linear-gradient(135deg, #00b4e6 0%, #0066e6 100%)' },
        { background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', hoverBackground: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)' },
        { background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)', hoverBackground: 'linear-gradient(135deg, #e084ea 0%, #e4485b 100%)' },
        { background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)', hoverBackground: 'linear-gradient(135deg, #3e9bfd 0%, #00e1fd 100%)' },
        { background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)', hoverBackground: 'linear-gradient(135deg, #2dd4bf 0%, #06b6d4 100%)' },
        { background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)', hoverBackground: 'linear-gradient(135deg, #f9618b 0%, #fdd831 100%)' },
        { background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)', hoverBackground: 'linear-gradient(135deg, #96e6e1 0%, #fcc9d6 100%)' },
        { background: 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)', hoverBackground: 'linear-gradient(135deg, #ff8a95 0%, #fdbde2 100%)' },
        { background: 'linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%)', hoverBackground: 'linear-gradient(135deg, #9575cd 0%, #f8bbd9 100%)' },
        { background: 'linear-gradient(135deg, #fad0c4 0%, #ffd1ff 100%)', hoverBackground: 'linear-gradient(135deg, #f9c5b8 0%, #ffbfff 100%)' },

        // 60-69: 黄色系列
        { background: 'linear-gradient(135deg, #f7971e 0%, #ffd200 100%)', hoverBackground: 'linear-gradient(135deg, #e6880e 0%, #e6c200 100%)' },
        { background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', hoverBackground: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)' },
        { background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)', hoverBackground: 'linear-gradient(135deg, #e084ea 0%, #e4485b 100%)' },
        { background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)', hoverBackground: 'linear-gradient(135deg, #3e9bfd 0%, #00e1fd 100%)' },
        { background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)', hoverBackground: 'linear-gradient(135deg, #2dd4bf 0%, #06b6d4 100%)' },
        { background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)', hoverBackground: 'linear-gradient(135deg, #f9618b 0%, #fdd831 100%)' },
        { background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)', hoverBackground: 'linear-gradient(135deg, #96e6e1 0%, #fcc9d6 100%)' },
        { background: 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)', hoverBackground: 'linear-gradient(135deg, #ff8a95 0%, #fdbde2 100%)' },
        { background: 'linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%)', hoverBackground: 'linear-gradient(135deg, #9575cd 0%, #f8bbd9 100%)' },
        { background: 'linear-gradient(135deg, #fad0c4 0%, #ffd1ff 100%)', hoverBackground: 'linear-gradient(135deg, #f9c5b8 0%, #ffbfff 100%)' },

        // 70-79: 粉色系列
        { background: 'linear-gradient(135deg, #ff758c 0%, #ff7eb3 100%)', hoverBackground: 'linear-gradient(135deg, #ff6179 0%, #ff6ba0 100%)' },
        { background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', hoverBackground: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)' },
        { background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)', hoverBackground: 'linear-gradient(135deg, #e084ea 0%, #e4485b 100%)' },
        { background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)', hoverBackground: 'linear-gradient(135deg, #3e9bfd 0%, #00e1fd 100%)' },
        { background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)', hoverBackground: 'linear-gradient(135deg, #2dd4bf 0%, #06b6d4 100%)' },
        { background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)', hoverBackground: 'linear-gradient(135deg, #f9618b 0%, #fdd831 100%)' },
        { background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)', hoverBackground: 'linear-gradient(135deg, #96e6e1 0%, #fcc9d6 100%)' },
        { background: 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)', hoverBackground: 'linear-gradient(135deg, #ff8a95 0%, #fdbde2 100%)' },
        { background: 'linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%)', hoverBackground: 'linear-gradient(135deg, #9575cd 0%, #f8bbd9 100%)' },
        { background: 'linear-gradient(135deg, #fad0c4 0%, #ffd1ff 100%)', hoverBackground: 'linear-gradient(135deg, #f9c5b8 0%, #ffbfff 100%)' },

        // 80-89: 灰色系列
        { background: 'linear-gradient(135deg, #bdc3c7 0%, #2c3e50 100%)', hoverBackground: 'linear-gradient(135deg, #a8b0b5 0%, #253544 100%)' },
        { background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', hoverBackground: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)' },
        { background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)', hoverBackground: 'linear-gradient(135deg, #e084ea 0%, #e4485b 100%)' },
        { background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)', hoverBackground: 'linear-gradient(135deg, #3e9bfd 0%, #00e1fd 100%)' },
        { background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)', hoverBackground: 'linear-gradient(135deg, #2dd4bf 0%, #06b6d4 100%)' },
        { background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)', hoverBackground: 'linear-gradient(135deg, #f9618b 0%, #fdd831 100%)' },
        { background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)', hoverBackground: 'linear-gradient(135deg, #96e6e1 0%, #fcc9d6 100%)' },
        { background: 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)', hoverBackground: 'linear-gradient(135deg, #ff8a95 0%, #fdbde2 100%)' },
        { background: 'linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%)', hoverBackground: 'linear-gradient(135deg, #9575cd 0%, #f8bbd9 100%)' },
        { background: 'linear-gradient(135deg, #fad0c4 0%, #ffd1ff 100%)', hoverBackground: 'linear-gradient(135deg, #f9c5b8 0%, #ffbfff 100%)' },

        // 90-99: 混合色系列
        { background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', hoverBackground: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)' },
        { background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)', hoverBackground: 'linear-gradient(135deg, #e084ea 0%, #e4485b 100%)' },
        { background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)', hoverBackground: 'linear-gradient(135deg, #3e9bfd 0%, #00e1fd 100%)' },
        { background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)', hoverBackground: 'linear-gradient(135deg, #2dd4bf 0%, #06b6d4 100%)' },
        { background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)', hoverBackground: 'linear-gradient(135deg, #f9618b 0%, #fdd831 100%)' },
        { background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)', hoverBackground: 'linear-gradient(135deg, #96e6e1 0%, #fcc9d6 100%)' },
        { background: 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)', hoverBackground: 'linear-gradient(135deg, #ff8a95 0%, #fdbde2 100%)' },
        { background: 'linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%)', hoverBackground: 'linear-gradient(135deg, #9575cd 0%, #f8bbd9 100%)' },
        { background: 'linear-gradient(135deg, #fad0c4 0%, #ffd1ff 100%)', hoverBackground: 'linear-gradient(135deg, #f9c5b8 0%, #ffbfff 100%)' },
        { background: 'linear-gradient(135deg, #ff9068 0%, #fd746c 100%)', hoverBackground: 'linear-gradient(135deg, #ff7f50 0%, #fc6254 100%)' }
      ]

      // 使用模运算确保不会超出数组范围
      const schemeIndex = this.level % colorSchemes.length
      return colorSchemes[schemeIndex]
    },
    
    getItemCount() {
      if (this.groupData.isLeaf) {
        return this.groupData.items.length
      } else {
        return this.groupData.totalItems
      }
    }
  }
}
</script>

<style scoped>
/* 嵌套分组样式 */
.nested-group {
  border-bottom: 1px solid #ebeef5;
}

.nested-group:last-child {
  border-bottom: none;
}

/* 分组标题基础样式 - 具体样式通过 JavaScript 动态计算 */
.group-header {
  /* 基础样式已通过 getHeaderStyle() 方法动态设置 */
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* 更深层级使用默认样式 */
.group-header {
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: all 0.3s;
}

/* 如果层级超过3，使用渐变的背景色 */
.nested-group[class*="level-"]:not(.level-0):not(.level-1):not(.level-2):not(.level-3) .group-header {
  padding: 8px;
  background-color: #f5f7fa;
  color: #606266;
  border-bottom: 1px solid #e4e7ed;
}

.nested-group[class*="level-"]:not(.level-0):not(.level-1):not(.level-2):not(.level-3) .group-header:hover {
  background-color: #e4e7ed;
}

.group-title {
  font-weight: 600;
  margin-left: 8px;
  font-size: 14px;
}

.group-count {
  margin-left: 8px;
  font-size: 12px;
  opacity: 0.8;
}

.group-statistics {
  margin-left: auto;
  font-size: 12px;
  opacity: 0.9;
}

/* 分组内容样式 */
.group-content {
  background-color: #fff;
}

.group-table {
  padding: 0;
}

.sub-groups {
  padding: 0;
}

/* 不同层级的内容缩进 */
.level-0-content {
  margin-left: 0;
}

/* 层级缩进已禁用 */
/*
.level-1-content {
  margin-left: 16px;
}

.level-2-content {
  margin-left: 32px;
}

.level-3-content {
  margin-left: 48px;
}
*/
</style>
