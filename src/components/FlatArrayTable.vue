<template>
    <div class="flat-array-table">
        <div class="demo-header">
            <h1>一维数组多维表格</h1>
            <p>通过一维数组实现分组和数据行混合的多维表格展示</p>
        </div>
        
        <!-- 控制面板 -->
        <div class="controls">
            <div class="control-item">
                <label>显示模式：</label>
                <select v-model="displayMode" @change="handleModeChange" class="control-select">
                    <option value="flat">一维数组模式</option>
                    <option value="grouped">分组展示模式</option>
                </select>
            </div>
            
            <div class="control-item">
                <button @click="addGroupRow" class="btn btn-primary">添加分组行</button>
                <button @click="addDataRow" class="btn btn-success">添加数据行</button>
                <button @click="exportData" class="btn btn-info">导出数据</button>
            </div>
        </div>

        <!-- 一维数组表格 -->
        <div v-if="displayMode === 'flat'" class="flat-table">
            <table class="data-table">
                <thead>
                    <tr>
                        <th width="80">类型</th>
                        <th width="120">ID/分组名</th>
                        <th width="150">名称/标题</th>
                        <th width="120">分类</th>
                        <th width="120">店铺</th>
                        <th width="100">价格</th>
                        <th>描述/地址</th>
                        <th width="120">操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="(item, index) in tableData" :key="index" 
                        :class="['data-row', item.type === 'group' ? 'group-row' : 'item-row']">
                        <td>
                            <span :class="['type-badge', item.type === 'group' ? 'badge-group' : 'badge-item']">
                                {{ item.type === 'group' ? '分组' : '数据' }}
                            </span>
                        </td>
                        <td>{{ item.type === 'group' ? item.groupName : item.id }}</td>
                        <td>{{ item.type === 'group' ? item.title : item.name }}</td>
                        <td>{{ item.category || '-' }}</td>
                        <td>{{ item.shop || '-' }}</td>
                        <td>{{ item.price ? '¥' + item.price : '-' }}</td>
                        <td class="desc" :title="item.desc || item.address">{{ item.desc || item.address || '-' }}</td>
                        <td>
                            <button @click="editItem(item, index)" class="btn btn-small btn-primary">编辑</button>
                            <button @click="deleteItem(index)" class="btn btn-small btn-danger">删除</button>
                            <button @click="moveUp(index)" class="btn btn-small" :disabled="index === 0">上移</button>
                            <button @click="moveDown(index)" class="btn btn-small" :disabled="index === tableData.length - 1">下移</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 分组展示模式 -->
        <div v-else class="grouped-display">
            <div v-for="(group, groupIndex) in groupedDisplay" :key="groupIndex" class="group-section">
                <!-- 分组标题 -->
                <div class="group-header" @click="toggleGroup(groupIndex)">
                    <span :class="['arrow', group.collapsed ? 'arrow-right' : 'arrow-down']">▶</span>
                    <span class="group-title">{{ group.title }}</span>
                    <span class="group-count">({{ group.items.length }}项)</span>
                    <div class="group-statistics">
                        <span>总计: {{ group.items.length }} 条记录</span>
                        <span v-if="group.summary" class="summary-info">
                            | 平均价格: ¥{{ group.summary.avgPrice }}
                        </span>
                    </div>
                </div>
                
                <!-- 分组内容 -->
                <div v-show="!group.collapsed" class="group-content">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th width="120">商品 ID</th>
                                <th width="150">商品名称</th>
                                <th width="120">分类</th>
                                <th width="120">店铺</th>
                                <th width="100">价格</th>
                                <th>描述</th>
                                <th width="100">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="item in group.items" :key="item.id" class="data-row">
                                <td>{{ item.id }}</td>
                                <td>{{ item.name }}</td>
                                <td>{{ item.category }}</td>
                                <td>{{ item.shop }}</td>
                                <td class="price">¥{{ item.price }}</td>
                                <td class="desc" :title="item.desc">{{ item.desc }}</td>
                                <td>
                                    <button @click="editDataItem(item)" class="btn btn-small btn-primary">编辑</button>
                                    <button @click="deleteDataItem(item)" class="btn btn-small btn-danger">删除</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 编辑对话框 -->
        <div v-if="showEditDialog" class="edit-dialog-overlay" @click="closeEditDialog">
            <div class="edit-dialog" @click.stop>
                <h3>{{ editingItem.type === 'group' ? '编辑分组' : '编辑数据' }}</h3>
                <div class="form-group" v-if="editingItem.type === 'group'">
                    <label>分组名称:</label>
                    <input v-model="editingItem.groupName" class="form-input">
                </div>
                <div class="form-group" v-if="editingItem.type === 'group'">
                    <label>分组标题:</label>
                    <input v-model="editingItem.title" class="form-input">
                </div>
                <div class="form-group" v-if="editingItem.type === 'item'">
                    <label>商品ID:</label>
                    <input v-model="editingItem.id" class="form-input">
                </div>
                <div class="form-group" v-if="editingItem.type === 'item'">
                    <label>商品名称:</label>
                    <input v-model="editingItem.name" class="form-input">
                </div>
                <div class="form-group" v-if="editingItem.type === 'item'">
                    <label>分类:</label>
                    <input v-model="editingItem.category" class="form-input">
                </div>
                <div class="form-group" v-if="editingItem.type === 'item'">
                    <label>店铺:</label>
                    <input v-model="editingItem.shop" class="form-input">
                </div>
                <div class="form-group" v-if="editingItem.type === 'item'">
                    <label>价格:</label>
                    <input v-model.number="editingItem.price" type="number" step="0.01" class="form-input">
                </div>
                <div class="form-group">
                    <label>{{ editingItem.type === 'group' ? '地址' : '描述' }}:</label>
                    <textarea v-model="editingItem.type === 'group' ? editingItem.address : editingItem.desc" class="form-textarea"></textarea>
                </div>
                <div class="dialog-actions">
                    <button @click="saveEdit" class="btn btn-primary">保存</button>
                    <button @click="closeEditDialog" class="btn">取消</button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'FlatArrayTable',
    data() {
        return {
            displayMode: 'flat', // 'flat' 或 'grouped'
            groupCollapsed: {}, // 分组折叠状态
            showEditDialog: false,
            editingItem: {},
            editingIndex: -1,
            tableData: [
                // 分组1
                {
                    type: 'group',
                    groupName: 'group1',
                    title: '江浙小吃',
                    address: '上海市普陀区真北路'
                },
                // 分组1的数据
                {
                    type: 'item',
                    id: '12987122',
                    name: '好滋好味鸡蛋仔',
                    category: '江浙小吃',
                    desc: '荷兰优质淡奶，奶香浓而不腻',
                    address: '上海市普陀区真北路',
                    shop: '王小虎夫妻店',
                    shopId: '10333',
                    price: 15.8
                },
                {
                    type: 'item',
                    id: '12987125',
                    name: '蒸蛋羹',
                    category: '江浙小吃',
                    desc: '嫩滑蒸蛋，营养丰富',
                    address: '上海市普陀区真北路',
                    shop: '王小虎夫妻店',
                    shopId: '10333',
                    price: 12.0
                },
                // 分组2
                {
                    type: 'group',
                    groupName: 'group2',
                    title: '川湘菜',
                    address: '成都市锦江区春熙路'
                },
                // 分组2的数据
                {
                    type: 'item',
                    id: '12987123',
                    name: '麻辣小龙虾',
                    category: '川湘菜',
                    desc: '新鲜小龙虾，麻辣鲜香',
                    address: '上海市黄浦区南京路',
                    shop: '李记海鲜',
                    shopId: '10334',
                    price: 68.0
                },
                {
                    type: 'item',
                    id: '12987126',
                    name: '麻婆豆腐',
                    category: '川湘菜',
                    desc: '经典川菜，麻辣鲜香',
                    address: '成都市锦江区春熙路',
                    shop: '川味轩',
                    shopId: '10336',
                    price: 28.0
                },
                // 分组3
                {
                    type: 'group',
                    groupName: 'group3',
                    title: '京菜',
                    address: '北京市朝阳区建国路'
                },
                // 分组3的数据
                {
                    type: 'item',
                    id: '12987124',
                    name: '北京烤鸭',
                    category: '京菜',
                    desc: '正宗北京烤鸭，皮脆肉嫩',
                    address: '北京市朝阳区建国路',
                    shop: '全聚德',
                    shopId: '10335',
                    price: 128.0
                }
            ]
        }
    },
    computed: {
        // 分组展示数据
        groupedDisplay() {
            const groups = []
            let currentGroup = null
            
            this.tableData.forEach((item, index) => {
                if (item.type === 'group') {
                    if (currentGroup) {
                        groups.push(currentGroup)
                    }
                    currentGroup = {
                        ...item,
                        items: [],
                        collapsed: this.groupCollapsed[index] || false,
                        summary: null
                    }
                } else if (item.type === 'item' && currentGroup) {
                    currentGroup.items.push(item)
                }
            })
            
            if (currentGroup) {
                groups.push(currentGroup)
            }
            
            // 计算统计信息
            groups.forEach(group => {
                if (group.items.length > 0) {
                    const totalPrice = group.items.reduce((sum, item) => sum + (item.price || 0), 0)
                    group.summary = {
                        avgPrice: (totalPrice / group.items.length).toFixed(2),
                        totalPrice: totalPrice.toFixed(2),
                        count: group.items.length
                    }
                }
            })
            
            return groups
        }
    },
    methods: {
        // 处理显示模式变化
        handleModeChange() {
            // 可以在这里添加模式切换的逻辑
        },

        // 切换分组折叠状态
        toggleGroup(groupIndex) {
            this.$set(this.groupCollapsed, groupIndex, !this.groupCollapsed[groupIndex])
        },

        // 添加分组行
        addGroupRow() {
            const newGroup = {
                type: 'group',
                groupName: `group${Date.now()}`,
                title: '新分组',
                address: '新地址'
            }
            this.tableData.push(newGroup)
        },

        // 添加数据行
        addDataRow() {
            const newItem = {
                type: 'item',
                id: `${Date.now()}`,
                name: '新商品',
                category: '未分类',
                desc: '新商品描述',
                address: '新地址',
                shop: '新店铺',
                shopId: `${Date.now()}`,
                price: 0
            }
            this.tableData.push(newItem)
        },

        // 编辑项目
        editItem(item, index) {
            this.editingItem = { ...item }
            this.editingIndex = index
            this.showEditDialog = true
        },

        // 编辑数据项目（分组模式下）
        editDataItem(item) {
            const index = this.tableData.findIndex(data => data.id === item.id && data.type === 'item')
            if (index > -1) {
                this.editItem(item, index)
            }
        },

        // 保存编辑
        saveEdit() {
            if (this.editingIndex > -1) {
                this.$set(this.tableData, this.editingIndex, { ...this.editingItem })
            }
            this.closeEditDialog()
        },

        // 关闭编辑对话框
        closeEditDialog() {
            this.showEditDialog = false
            this.editingItem = {}
            this.editingIndex = -1
        },

        // 删除项目
        deleteItem(index) {
            const item = this.tableData[index]
            const itemType = item.type === 'group' ? '分组' : '数据项'
            const itemName = item.type === 'group' ? item.title : item.name

            if (confirm(`确定要删除${itemType} "${itemName}" 吗？`)) {
                this.tableData.splice(index, 1)
            }
        },

        // 删除数据项目（分组模式下）
        deleteDataItem(item) {
            const index = this.tableData.findIndex(data => data.id === item.id && data.type === 'item')
            if (index > -1) {
                this.deleteItem(index)
            }
        },

        // 上移
        moveUp(index) {
            if (index > 0) {
                const item = this.tableData.splice(index, 1)[0]
                this.tableData.splice(index - 1, 0, item)
            }
        },

        // 下移
        moveDown(index) {
            if (index < this.tableData.length - 1) {
                const item = this.tableData.splice(index, 1)[0]
                this.tableData.splice(index + 1, 0, item)
            }
        },

        // 导出数据
        exportData() {
            const dataStr = JSON.stringify({ tableData: this.tableData }, null, 2)
            const blob = new Blob([dataStr], { type: 'application/json' })
            const url = URL.createObjectURL(blob)
            const a = document.createElement('a')
            a.href = url
            a.download = 'flat-array-table-data.json'
            document.body.appendChild(a)
            a.click()
            document.body.removeChild(a)
            URL.revokeObjectURL(url)
        }
    }
}
</script>

<style scoped>
.flat-array-table {
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.demo-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px;
}

.demo-header h1 {
    margin: 0 0 10px 0;
    font-size: 28px;
    font-weight: 600;
}

.demo-header p {
    margin: 0;
    font-size: 16px;
    opacity: 0.9;
}

.controls {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f5f7fa;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
}

.control-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.control-item label {
    font-weight: 500;
    color: #606266;
    font-size: 14px;
}

.control-select {
    padding: 8px 12px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    background-color: #fff;
    font-size: 14px;
    min-width: 150px;
}

.control-select:focus {
    outline: none;
    border-color: #409eff;
}

.btn {
    padding: 6px 12px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    background-color: #fff;
    color: #606266;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s;
    margin-right: 5px;
}

.btn:hover:not(:disabled) {
    background-color: #f5f7fa;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-small {
    padding: 4px 8px;
    font-size: 11px;
}

.btn-primary {
    background-color: #409eff;
    border-color: #409eff;
    color: #fff;
}

.btn-primary:hover {
    background-color: #66b1ff;
}

.btn-success {
    background-color: #67c23a;
    border-color: #67c23a;
    color: #fff;
}

.btn-success:hover {
    background-color: #85ce61;
}

.btn-info {
    background-color: #909399;
    border-color: #909399;
    color: #fff;
}

.btn-info:hover {
    background-color: #a6a9ad;
}

.btn-danger {
    background-color: #f56c6c;
    border-color: #f56c6c;
    color: #fff;
}

.btn-danger:hover {
    background-color: #f78989;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
    border: 1px solid #ebeef5;
    border-radius: 8px;
    overflow: hidden;
}

.data-table th {
    background-color: #fafafa;
    color: #909399;
    font-weight: 500;
    padding: 12px 8px;
    text-align: left;
    border-bottom: 1px solid #ebeef5;
}

.data-table td {
    padding: 12px 8px;
    border-bottom: 1px solid #f0f0f0;
    color: #606266;
}

.data-row:hover {
    background-color: #f5f7fa;
}

.group-row {
    background-color: #f0f9ff;
}

.group-row:hover {
    background-color: #e0f2fe;
}

.item-row {
    background-color: #fff;
}

.type-badge {
    padding: 2px 6px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

.badge-group {
    background-color: #e1f5fe;
    color: #0277bd;
}

.badge-item {
    background-color: #f3e5f5;
    color: #7b1fa2;
}

.price {
    font-weight: 600;
    color: #e6a23c;
}

.desc {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 分组展示样式 */
.grouped-display {
    border: 1px solid #ebeef5;
    border-radius: 8px;
    overflow: hidden;
}

.group-section {
    border-bottom: 1px solid #ebeef5;
}

.group-section:last-child {
    border-bottom: none;
}

.group-header {
    padding: 12px 16px;
    background-color: #fafafa;
    border-bottom: 1px solid #ebeef5;
    cursor: pointer;
    display: flex;
    align-items: center;
    transition: background-color 0.3s;
    user-select: none;
}

.group-header:hover {
    background-color: #f0f2f5;
}

.arrow {
    font-size: 12px;
    color: #909399;
    transition: transform 0.3s;
    margin-right: 8px;
}

.arrow-right {
    transform: rotate(0deg);
}

.arrow-down {
    transform: rotate(90deg);
}

.group-title {
    font-weight: 600;
    color: #303133;
    font-size: 14px;
}

.group-count {
    margin-left: 8px;
    color: #909399;
    font-size: 12px;
}

.group-statistics {
    margin-left: auto;
    color: #606266;
    font-size: 12px;
}

.summary-info {
    margin-left: 10px;
    color: #409eff;
}

.group-content {
    background-color: #fff;
}

/* 编辑对话框样式 */
.edit-dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.edit-dialog {
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    min-width: 400px;
    max-width: 500px;
    max-height: 80vh;
    overflow-y: auto;
}

.edit-dialog h3 {
    margin: 0 0 20px 0;
    color: #303133;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #606266;
}

.form-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    font-size: 14px;
    box-sizing: border-box;
}

.form-input:focus {
    outline: none;
    border-color: #409eff;
}

.form-textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    font-size: 14px;
    min-height: 80px;
    resize: vertical;
    box-sizing: border-box;
}

.form-textarea:focus {
    outline: none;
    border-color: #409eff;
}

.dialog-actions {
    margin-top: 20px;
    text-align: right;
}

.dialog-actions .btn {
    margin-left: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .controls {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .control-item {
        width: 100%;
        justify-content: space-between;
    }

    .data-table {
        font-size: 12px;
    }

    .data-table th,
    .data-table td {
        padding: 8px 4px;
    }

    .desc {
        max-width: 100px;
    }

    .edit-dialog {
        min-width: 300px;
        margin: 20px;
    }
}

@media (max-width: 480px) {
    .flat-array-table {
        padding: 10px;
    }

    .controls {
        padding: 10px;
    }

    .data-table th,
    .data-table td {
        padding: 6px 2px;
        font-size: 11px;
    }

    .btn-small {
        padding: 2px 4px;
        font-size: 10px;
    }
}
</style>



<!-- {
    "tableData": [
        {
            // 分组1
        },
        {
            // 分组2
        },
        {
            "id": "12987122",
            "name": "好滋好味鸡蛋仔",
            "category": "江浙小吃",
            "desc": "荷兰优质淡奶，奶香浓而不腻",
            "address": "上海市普陀区真北路",
            "shop": "王小虎夫妻店",
            "shopId": "10333",
            "price": 15.8
        },
        {
            // 分组1
        },
        {
            // 分组2
        },
        {
            "id": "12987123",
            "name": "麻辣小龙虾",
            "category": "川湘菜",
            "desc": "新鲜小龙虾，麻辣鲜香",
            "address": "上海市黄浦区南京路",
            "shop": "李记海鲜",
            "shopId": "10334",
            "price": 68.0
        }
    ]
} -->