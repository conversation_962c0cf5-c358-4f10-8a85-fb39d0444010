<template>
  <div id="app">
    <h1>Teable 风格分组表格示例</h1>
    
    <!-- 使用分组表格组件 -->
    <GroupedTable :data="tableData" :group-fields="groupFields">
      <template #columns="{ data }">
        <el-table-column label="商品 ID" prop="id" width="120"></el-table-column>
        <el-table-column label="商品名称" prop="name" width="150" show-overflow-tooltip></el-table-column>
        <el-table-column label="价格" prop="price" width="100">
          <template slot-scope="scope">
            <span style="color: #f56c6c; font-weight: bold;">¥{{ scope.row.price }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="status" width="100">
          <template slot-scope="scope">
            <el-tag :type="getStatusType(scope.row.status)" size="small">{{ scope.row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="描述" prop="desc" show-overflow-tooltip></el-table-column>
      </template>
    </GroupedTable>

    <!-- 数据操作按钮 -->
    <div class="action-buttons">
      <el-button type="primary" @click="addRandomData">添加随机数据</el-button>
      <el-button @click="clearData">清空数据</el-button>
      <el-button @click="resetData">重置数据</el-button>
    </div>
  </div>
</template>

<script>
import GroupedTable from './components/GroupedTable.vue'

export default {
  name: 'App',
  components: {
    GroupedTable
  },
  data() {
    return {
      // 分组字段配置
      groupFields: [
        { label: '按分类分组', value: 'category' },
        { label: '按店铺分组', value: 'shop' },
        { label: '按地址分组', value: 'address' },
        { label: '按状态分组', value: 'status' },
        { label: '按价格区间分组', value: 'priceRange' }
      ],
      // 表格数据
      tableData: [
        {
          id: '12987122',
          name: '好滋好味鸡蛋仔',
          category: '江浙小吃',
          desc: '荷兰优质淡奶，奶香浓而不腻',
          address: '上海市普陀区',
          shop: '王小虎夫妻店',
          price: 25.8,
          status: '在售',
          priceRange: '20-30元'
        },
        {
          id: '12987123',
          name: '麻辣小龙虾',
          category: '川湘菜',
          desc: '新鲜小龙虾，麻辣鲜香',
          address: '上海市黄浦区',
          shop: '李记海鲜',
          price: 68.0,
          status: '在售',
          priceRange: '60-80元'
        },
        {
          id: '12987124',
          name: '北京烤鸭',
          category: '京菜',
          desc: '正宗北京烤鸭，皮脆肉嫩',
          address: '北京市朝阳区',
          shop: '全聚德',
          price: 128.0,
          status: '缺货',
          priceRange: '100元以上'
        },
        {
          id: '12987125',
          name: '蒸蛋羹',
          category: '江浙小吃',
          desc: '嫩滑蒸蛋，营养丰富',
          address: '上海市普陀区',
          shop: '王小虎夫妻店',
          price: 15.0,
          status: '在售',
          priceRange: '10-20元'
        },
        {
          id: '12987126',
          name: '麻婆豆腐',
          category: '川湘菜',
          desc: '经典川菜，麻辣鲜香',
          address: '成都市锦江区',
          shop: '川味轩',
          price: 32.0,
          status: '在售',
          priceRange: '30-40元'
        },
        {
          id: '12987127',
          name: '宫保鸡丁',
          category: '川湘菜',
          desc: '传统川菜，酸甜可口',
          address: '上海市黄浦区',
          shop: '李记海鲜',
          price: 38.0,
          status: '停售',
          priceRange: '30-40元'
        },
        {
          id: '12987128',
          name: '小笼包',
          category: '江浙小吃',
          desc: '皮薄馅大，汤汁鲜美',
          address: '上海市黄浦区',
          shop: '南翔小笼',
          price: 22.0,
          status: '在售',
          priceRange: '20-30元'
        },
        {
          id: '12987129',
          name: '水煮鱼',
          category: '川湘菜',
          desc: '鱼肉鲜嫩，麻辣过瘾',
          address: '成都市锦江区',
          shop: '川味轩',
          price: 58.0,
          status: '在售',
          priceRange: '50-60元'
        }
      ]
    }
  },
  methods: {
    // 获取状态标签类型
    getStatusType(status) {
      const statusMap = {
        '在售': 'success',
        '缺货': 'warning',
        '停售': 'danger'
      }
      return statusMap[status] || 'info'
    },
    
    // 编辑操作
    handleEdit(row) {
      this.$message.info(`编辑商品: ${row.name}`)
    },
    
    // 删除操作
    handleDelete(row) {
      this.$confirm(`确定删除商品 ${row.name} 吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.tableData.findIndex(item => item.id === row.id)
        if (index > -1) {
          this.tableData.splice(index, 1)
          this.$message.success('删除成功!')
        }
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },
    
    // 添加随机数据
    addRandomData() {
      const categories = ['江浙小吃', '川湘菜', '京菜', '粤菜', '东北菜']
      const shops = ['王小虎夫妻店', '李记海鲜', '全聚德', '川味轩', '南翔小笼', '老北京炸酱面']
      const addresses = ['上海市普陀区', '上海市黄浦区', '北京市朝阳区', '成都市锦江区', '广州市天河区']
      const statuses = ['在售', '缺货', '停售']
      const names = ['红烧肉', '糖醋排骨', '鱼香肉丝', '回锅肉', '白切鸡', '蒜蓉菜心']
      
      const randomItem = {
        id: Date.now().toString(),
        name: names[Math.floor(Math.random() * names.length)],
        category: categories[Math.floor(Math.random() * categories.length)],
        desc: '美味可口，值得品尝',
        address: addresses[Math.floor(Math.random() * addresses.length)],
        shop: shops[Math.floor(Math.random() * shops.length)],
        price: Math.floor(Math.random() * 100) + 10,
        status: statuses[Math.floor(Math.random() * statuses.length)]
      }
      
      // 根据价格设置价格区间
      if (randomItem.price < 20) {
        randomItem.priceRange = '10-20元'
      } else if (randomItem.price < 30) {
        randomItem.priceRange = '20-30元'
      } else if (randomItem.price < 40) {
        randomItem.priceRange = '30-40元'
      } else if (randomItem.price < 60) {
        randomItem.priceRange = '50-60元'
      } else if (randomItem.price < 80) {
        randomItem.priceRange = '60-80元'
      } else {
        randomItem.priceRange = '100元以上'
      }
      
      this.tableData.push(randomItem)
      this.$message.success('添加成功!')
    },
    
    // 清空数据
    clearData() {
      this.$confirm('确定清空所有数据吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.tableData = []
        this.$message.success('清空成功!')
      })
    },
    
    // 重置数据
    resetData() {
      // 这里可以重置为初始数据
      location.reload()
    }
  }
}
</script>

<style>
#app {
  font-family: 'Avenir', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
  margin: 20px;
}

h1 {
  text-align: center;
  color: #409eff;
  margin-bottom: 30px;
}

.demo-table-expand {
  font-size: 0;
}

.demo-table-expand label {
  width: 90px;
  color: #99a9bf;
}

.demo-table-expand .el-form-item {
  margin-right: 0;
  margin-bottom: 0;
  width: 50%;
}

.action-buttons {
  margin-top: 20px;
  text-align: center;
}

.action-buttons .el-button {
  margin: 0 10px;
}
</style>
