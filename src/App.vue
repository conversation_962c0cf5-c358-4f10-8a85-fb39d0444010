<template>
  <el-table border :data="tableData" style="width: 100%" :cell-class-name="cellClassName"
    @cell-mouse-enter="handleCellMouseEnter" @cell-mouse-leave="handleCellMouseLeave">
    <el-table-column v-for="column in tableColumns" :key="column.prop" :prop="column.prop" :label="column.label"
      :width="column.width">
      <template slot-scope="scope">
        <span v-if="scope.$index !== tableData.length - 1" v-html="getCellValue(scope, column)"></span>
        <template v-else>
          <el-select size="mini" class="select-handler" @click.native="handleClickSelectHandler(column)"
            v-if="hasNumberSelector(column)" v-model="column.handlerType"
            @change="handleSelectHandlerChange(scope, column)">
            <el-option v-for="item in numberOptions" :key="item.value" :label="item.label"
              :value="item.value"></el-option>
          </el-select>
          <el-select size="mini" class="select-handler" @click.native="handleClickSelectHandler(column)"
            v-if="hasOtherSelector(column)" v-model="column.handlerType"
            @change="handleSelectHandlerChange(scope, column)">
            <el-option v-for="item in otherOptions" :key="item.value" :label="item.label"
              :value="item.value"></el-option>
          </el-select>
          <span v-if="column.handlerType !== 'nodisplay'">{{ getHandlerLable(column) }} {{ getCellValue(scope, column)
          }}</span>
        </template>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
export default {
  data() {
    return {
      tableColumns: [
        {
          prop: 'age',
          type: 'number',
          label: '年龄',
          enteredCell: false,
          clickedHandler: false,
          handlerType: "nodisplay",
        },
        {
          prop: 'date',
          type: 'date',
          label: '日期',
          enteredCell: false,
          clickedHandler: false,
          handlerType: "nodisplay",
        },
        {
          prop: 'name',
          type: 'string',
          label: '姓名',
          enteredCell: false,
          clickedHandler: false,
          handlerType: "nodisplay",
        },
        {
          prop: 'address',
          type: 'string',
          label: '地址',
          enteredCell: false,
          clickedHandler: false,
          handlerType: "nodisplay",
        }
      ],
      tableData: [{
        age: 18,
        date: '',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1518 弄'
      }, {
        age: null,
        date: '',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1517 弄'
      }, {
        age: 22,
        date: '2016-05-01',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1519 弄'
      }, {
        age: 24,
        date: '2016-05-03',
        name: '王小虎',
        address: ''
      },
      {
        age: null,
        date: null,
        name: null,
        address: null
      }],
      numberOptions: [
        {
          label: "不展示",
          value: "nodisplay"
        },
        {
          label: "最大",
          value: "max"
        }, {
          label: "最小",
          value: "min"
        }, {
          label: "平均",
          value: "avg"
        }, {
          label: "求和",
          value: "sum"
        }],
      otherOptions: [
        {
          label: "不展示",
          value: "nodisplay"
        }, {
          label: "已填写",
          value: "filled"
        }, {
          label: "未填写",
          value: "empty"
        }]
    }
  },
  methods: {
    /**
     * 是否展示数字列下拉选
     * @param column 
     * @returns 
     */
    hasNumberSelector(column) {
      return (column.enteredCell || column.clickedHandler) && column.type === 'number'
    },
    /**
     * 是否展示非数字列下拉选
     * @param column 
     * @returns 
     */
    hasOtherSelector(column) {
      return (column.enteredCell || column.clickedHandler) && column.type !== 'number'
    },
    /**
     * 单元格样式
     * @returns 
     */
    cellClassName() {
      return "column-cell"
    },
    /**
     * 获取单元格值
     * @param scope 
     * @param column 
     * @returns 
     */
    getCellValue(scope, column) {
      const value = scope.row[column.prop];
      if (value === null || value === undefined) {
        return '-';
      } else {
        return value;
      }
    },
    /**
     * 点击下拉选
     * @param column 
     */
    handleClickSelectHandler(column) {
      column.clickedHandler = true;
    },
    /**
     * 获取有效数据
     * @returns 
     */
    hanleGetValidTableData(){
      return this.tableData.slice(0, -1).filter(item => item.age !== null && item.age !== undefined && item.age !== '');
    },
    /**
     * 最大值
     * @param scope 
     * @param column 
     */
    handleGetMaxValue(scope, column) {
      // 去掉最后一条数据 计算最大值
      const maxValue = Math.max(...this.hanleGetValidTableData().map(item => item[column.prop]));
      scope.row[column.prop] = maxValue;
      this.tableData[scope.$index][column.prop] = maxValue;
    },
    /**
     * 最小值
     * @param scope 
     * @param column 
     */
    handleGetMinValue(scope, column) {
      // 去掉最后一条数据 计算最小值
      const minValue = Math.min(...this.hanleGetValidTableData().map(item => item[column.prop]));
      scope.row[column.prop] = minValue;
      this.tableData[scope.$index][column.prop] = minValue;
    },
    /**
     * 平均值
     * @param scope 
     * @param column 
     */
    handleGetAvgVaue(scope, column) {
      // 去掉最后一条数据 计算平均值
      const avgValue = this.hanleGetValidTableData().map(item => item[column.prop]).reduce((a, b) => a + b, 0) / this.hanleGetValidTableData().length;
      scope.row[column.prop] = avgValue;
      this.tableData[scope.$index][column.prop] = avgValue;
    },
    /**
     * 求和
     * @param scope 
     * @param column 
     */
    handleGetSumValue(scope, column) {
      // 去掉最后一条数据 计算求和
      const sumValue = this.hanleGetValidTableData().map(item => item[column.prop]).reduce((a, b) => a + b, 0);
      scope.row[column.prop] = sumValue;
      this.tableData[scope.$index][column.prop] = sumValue;
    },
    /**
     * 已填写
     * @param scope 
     * @param column 
     */
    handleGetFilledValue(scope, column) {
      // 去掉最后一条数据 计算已填写
      const filledValue = this.tableData.slice(0, -1).filter(item => item[column.prop] !== null && item[column.prop] !== undefined && item[column.prop] !== '').length;
      scope.row[column.prop] = filledValue;
      this.tableData[scope.$index][column.prop] = filledValue;
    },
    /**
     * 未填写
     * @param scope 
     * @param column 
     */
    handleGetEmptyValue(scope, column) {
      // 去掉最后一条数据 计算未填写 未填写为空或者undefined
      const emptyValue = this.tableData.slice(0, -1).filter(item => item[column.prop] === null || item[column.prop] === undefined || item[column.prop] === '').length;
      scope.row[column.prop] = emptyValue;
      this.tableData[scope.$index][column.prop] = emptyValue;
    },
    /**
     * 下拉选 处理函数
     * @param scope 
     * @param column 
     */
    handleSelectHandlerChange(scope, column) {
      switch (column.handlerType) {
        case "max":
          this.handleGetMaxValue(scope, column)
          break;
        case "min":
          this.handleGetMinValue(scope, column)
          break;
        case "avg":
          this.handleGetAvgVaue(scope, column)
          break;
        case "sum":
          this.handleGetSumValue(scope, column)
          break;
        case "filled":
          this.handleGetFilledValue(scope, column)
          break;
        case "empty":
          this.handleGetEmptyValue(scope, column)
          break;
        case "nodisplay":
          break;
        default:
          console.error("未找到匹配的操作类型")
          break;
      }
      column.enteredCell = false;
      column.clickedHandler = false;
    },
    getHandlerLable(column) {
      if (column.type === "number") {
        return this.numberOptions.find(item => item.value === column.handlerType).label;
      } else {
        return this.otherOptions.find(item => item.value === column.handlerType).label;
      }
    },
    /**
     * 鼠标进入单元格 展示下拉选
     * @param row 
     * @param column 
     * @param cell 
     * @param event 
     */
    handleCellMouseEnter(row, column, cell, event) {
      const currentColumn = this.tableColumns.find(item => item.prop === column.property);
      currentColumn.enteredCell = true;
    },
    /**
     * 鼠标离开单元格 隐藏下拉选
     * @param row 
     * @param column 
     * @param cell 
     * @param event 
     */
    handleCellMouseLeave(row, column, cell, event) {
      const currentColumn = this.tableColumns.find(item => item.prop === column.property);
      currentColumn.enteredCell = false;
    }
  }
}
</script>
<style lang="scss" scoped>
:deep(.el-table__body-wrapper) {
  .el-table__body {
    .el-table__row {
      td {
        .cell {
          min-height: 28px;
          padding: 0 10px;
        }

        .select-handler {
          margin-right: 5px;
        }

        // .select-handler {
        //   display: none;
        // }
        // &:hover {
        //   .select-handler {
        //     display: block;
        //   }
        // }
      }
    }
  }
}
</style>